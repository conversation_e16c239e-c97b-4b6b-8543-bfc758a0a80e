{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/jonkrohn/ML-foundations/blob/master/notebooks/3-calculus-i.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "aTOLgsbN69-P"}, "source": ["# Calculus I: Limits & Derivatives"]}, {"cell_type": "markdown", "metadata": {"id": "yqUB9FTRAxd-"}, "source": ["This topic, *Calculus I: Limits & Derivatives*, introduces the mathematical field of calculus -- the study of rates of change -- from the ground up. It is essential because computing derivatives via differentiation is the basis of optimizing most machine learning algorithms, including those used in deep learning such as backpropagation and stochastic gradient descent. \n", "\n", "Through the measured exposition of theory paired with interactive examples, you’ll develop a working understanding of how calculus is used to compute limits and differentiate functions. You’ll also learn how to apply automatic differentiation within the popular TensorFlow 2 and PyTorch machine learning libraries. The content covered in this class is itself foundational for several other topics in the *Machine Learning Foundations* series, especially *Calculus II* and *Optimization*."]}, {"cell_type": "markdown", "metadata": {"id": "d4tBvI88BheF"}, "source": ["Over the course of studying this topic, you'll: \n", "\n", "* Develop an understanding of what’s going on beneath the hood of machine learning algorithms, including those used for deep learning. \n", "* Be able to more intimately grasp the details of machine learning papers as well as many of the other subjects that underlie ML, including partial-derivative calculus, statistics and optimization algorithms. \n", "* Compute the derivatives of functions, including by using AutoDiff in the popular TensorFlow 2 and PyTorch libraries."]}, {"cell_type": "markdown", "metadata": {"id": "Z68nQ0ekCYhF"}, "source": ["**Note that this <PERSON><PERSON><PERSON> notebook is not intended to stand alone. It is the companion code to a lecture or to videos from <PERSON>'s [Machine Learning Foundations](https://github.com/jonk<PERSON><PERSON>/ML-foundations) series, which offer detail on the following:**\n", "\n", "*Segment 1: Limits*\n", "\n", "* What Calculus Is\n", "* A Brief History of Calculus\n", "* The Method of Exhaustion \n", "* Calculating Limits \n", "\n", "*Segment 2: Computing Derivatives with Differentiation*\n", "* The Delta Method\n", "* The Differentiation Equation\n", "* Derivative Notation\n", "* The Power Rule\n", "* The Constant Multiple Rule\n", "* The Sum Rule\n", "* The Product Rule\n", "* The Quotient Rule\n", "* The Chain Rule\n", "\n", "*Segment 3: Automatic Differentiation*\n", "* AutoDiff with PyTorch\n", "* AutoDiff with TensorFlow 2\n", "* Machine Learning via Differentiation \n", "* Cost (or Loss) Functions\n", "* The Future: Differentiable Programming \n"]}, {"cell_type": "markdown", "metadata": {"id": "1SHRQmz0cxYw"}, "source": ["## Segment 1: <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "ab2YePNtcxYx"}, "source": ["### The Calculus of Infinitesimals"]}, {"cell_type": "code", "execution_count": 103, "metadata": {"id": "Bzug184ZcxYx"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 104, "metadata": {"id": "Kgjg4q4kcxY1"}, "outputs": [], "source": ["x = np.linspace(-10, 10, 10000) # start, finish, n points"]}, {"cell_type": "markdown", "metadata": {"id": "MHWrqwBacxY4"}, "source": ["If $y = x^2 + 2x + 2$: "]}, {"cell_type": "code", "execution_count": 105, "metadata": {"id": "y4oTgUY_cxY5"}, "outputs": [], "source": ["y = x**2 + 2*x + 2"]}, {"cell_type": "code", "execution_count": 106, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "5OxJjePDcxY8", "outputId": "9ffb0b81-c1e3-4634-f552-c22713f36ed5"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAigAAAGdCAYAAAA44ojeAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAATzNJREFUeJzt3XlcVOXiBvBnBphhHwRk391wwV0RNZck11xKK83SyqtWWLm0+eum7ZrZppnaplZaaaWWpWWuqYgK7guCAoIIiMiwCTPMvL8/BueK4gIOnFme7+czn3s5c2Z4pgPM45n3vK9MCCFAREREZEbkUgcgIiIiuh4LChEREZkdFhQiIiIyOywoREREZHZYUIiIiMjssKAQERGR2WFBISIiIrPDgkJERERmx17qAHWh1+uRnZ0NNzc3yGQyqeMQERHRHRBCoLi4GAEBAZDLb32OxCILSnZ2NoKDg6WOQURERHWQmZmJoKCgW+5jkQXFzc0NgOEFuru7S5yGiIiI7kRRURGCg4ON7+O3YpEF5erHOu7u7iwoREREFuZOhmdwkCwRERGZHRYUIiIiMjssKERERGR2WFCIiIjI7LCgEBERkdlhQSEiIiKzw4JCREREZocFhYiIiMwOCwoRERGZHRYUIiIiMjssKERERGR2WFCIiIjI7LCgEBERkdGx82pMWZWEf1MuSprDIlczJiIiovrxc2IWNhy5AJlMhnuaNZYsB8+gEBEREQBAU6nH+kPnAQAjOwZKmqXWBWXnzp0YOnQoAgICIJPJsG7dOuN9Wq0Wr7zyCqKiouDi4oKAgACMGzcO2dnZ1Z6joKAAY8eOhbu7Ozw8PDBhwgSUlJTc9YshIiKiutuWnIfLZVr4uCklPXsC1KGglJaWol27dli0aNEN95WVlSEpKQmvv/46kpKS8OuvvyI5ORnDhg2rtt/YsWNx/PhxbN68GRs2bMDOnTsxadKkur8KIiIiumu/JGYBAB7oEAg7uUzSLDIhhKjzg2UyrF27FiNGjLjpPvv370fXrl2RkZGBkJAQnDx5Eq1atcL+/fvRuXNnAMCmTZswePBgZGVlISAg4Lbft6ioCCqVCmq1Gu7u7nWNT0RERFUKSjWIfu8faHUCf0/rhea+bib/HrV5/673MShqtRoymQweHh4AgPj4eHh4eBjLCQDExsZCLpcjISGhxueoqKhAUVFRtRsRERGZzm+HzkOrE4gKVNVLOamtei0o5eXleOWVVzBmzBhjU8rJyYGPj0+1/ezt7eHp6YmcnJwan2fOnDlQqVTGW3BwcH3GJiIisjm/JJnH4Nir6q2gaLVaPPzwwxBCYPHixXf1XDNnzoRarTbeMjMzTZSSiIiITucW4+h5NRzsZBjW3jwKSr3Mg3K1nGRkZGDr1q3VPmfy8/NDXl5etf0rKytRUFAAPz+/Gp9PqVRCqVTWR1QiIiKbt+aA4R/+fVv4wNNFIXEaA5OfQblaTlJSUvDPP//Ay8ur2v0xMTEoLCxEYmKicdvWrVuh1+sRHR1t6jhERER0C1qdHmsPGj7eeaiz+QyhqPUZlJKSEqSmphq/TktLw6FDh+Dp6Ql/f3+MGjUKSUlJ2LBhA3Q6nXFciaenJxQKBVq2bImBAwdi4sSJWLJkCbRaLaZMmYLRo0ff0RU8REREZDrbky8iv0QDb1cF+rSQdu6Ta9W6oBw4cAB9+/Y1fj19+nQAwPjx4/HGG2/gt99+AwC0b9++2uO2bduGPn36AABWrlyJKVOmoF+/fpDL5Rg5ciQWLFhQx5dAREREdXX1450HOgTCwc58JpivdUHp06cPbjV1yp1Mq+Lp6YlVq1bV9lsTERGRCeWXVGDrKcO4UHP6eAfgWjxEREQ2a93B86jUC7QLMo+5T67FgkJERGSDhBD4uWpq+1FmdvYEYEEhIiKyScfOF+FUTjEU9nIMa2t+F6mwoBAREdmgNYmGwbEDWvtB5ewgcZobsaAQERHZmHKtDusPZQMAHuoUJHGamrGgEBER2Zh/TuZCfUWLAJUjejT1ljpOjVhQiIiIbMyaA4bBsSM7BcFOLpM4Tc1YUIiIiGxIjroc/6ZcBACMMtOPdwAWFCIiIpvyS1IW9ALoGu6JUC8XqePcFAsKERGRjbh27hNzHRx7FQsKERGRjUjMuIy0/FI4K+wwOMpf6ji3xIJCRERkI1ZXLQw4JMofLspaL8fXoFhQiIiIbECZphJ/HLkAwPwWBqwJCwoREZEN+PNoDko1OoR5OaNLWCOp49wWCwoREZENWFP18c6oTkGQycxz7pNrsaAQERFZubT8UiSkFUAuAx7saN5X71zFgkJERGTlftpvOHvSu3ljBHg4SZzmzrCgEBERWTGtTm+c++SRLiESp7lzLChERERWbOupPOSXVMDbVYl+LX2kjnPHWFCIiIis2NWPd0Z1CoKDneW87VtOUiIiIqqVC+or2J6cBwB4pIv5z31yLRYUIiIiK/XzAcPCgNHhngj3Nt+FAWvCgkJERGSF9HqBn6rmPhnd1bLOngAsKERERFZp95l8ZF2+AjdHewxqY94LA9aEBYWIiMgK/Vg1OPaBDoFwdLCTOE3tsaAQERFZmYJSDf4+ngPA8gbHXsWCQkREZGV+TcqCVicQFahC6wCV1HHqhAWFiIjIigghjHOfWOLg2KtYUIiIiKxI0rnLSMkrgZODHYa1C5A6Tp2xoBAREVmRH/cZzp4MaesPN0cHidPUHQsKERGRlSgu12LDkQsAgNEWOjj2KhYUIiIiK/H74Qu4otWhSWMXdAptJHWcu8KCQkREZCV+2n8OADC6SwhkMpnEae4OCwoREZEVOJFdhMNZajjYyfBAx0Cp49w1FhQiIiIrsGpfBgCgfys/eLsqJU5z91hQiIiILFxpRSXWHcwGADwaHSJxGtNgQSEiIrJwvx3ORklFJcK9XRAT4SV1HJNgQSEiIrJwqxIMg2PHdA2GXG7Zg2OvYkEhIiKyYEeyCnH0vBoKOzlGdbLsuU+uxYJCRERkwa6ePRkU5QdPF4XEaUyHBYWIiMhCFZVr8dvhqsGxXa1jcOxVLChEREQWav3B8yjT6NDUxxVdwz2ljmNSLChEREQWSAiBlVUf7zza1fJnjr0eCwoREZEFSjpXiFM5xVDayzGyY5DUcUyu1gVl586dGDp0KAICAiCTybBu3bpq9wshMGvWLPj7+8PJyQmxsbFISUmptk9BQQHGjh0Ld3d3eHh4YMKECSgpKbmrF0JERGRLrg6Ovb9tAFTODhKnMb1aF5TS0lK0a9cOixYtqvH+efPmYcGCBViyZAkSEhLg4uKCAQMGoLy83LjP2LFjcfz4cWzevBkbNmzAzp07MWnSpLq/CiIiIhuiLtNiwxHD4Nix3axrcOxVMiGEqPODZTKsXbsWI0aMAGA4exIQEIAZM2bgxRdfBACo1Wr4+vpi+fLlGD16NE6ePIlWrVph//796Ny5MwBg06ZNGDx4MLKyshAQEHDb71tUVASVSgW1Wg13d/e6xiciIrJI3+xKw1sbTiDSzw0bX7jHYsaf1Ob926RjUNLS0pCTk4PY2FjjNpVKhejoaMTHxwMA4uPj4eHhYSwnABAbGwu5XI6EhARTxiEiIrI6Qgis2mf4eGdst1CLKSe1ZW/KJ8vJyQEA+Pr6Vtvu6+trvC8nJwc+Pj7VQ9jbw9PT07jP9SoqKlBRUWH8uqioyJSxiYiILMa+tAKk5pXAWWGHEe1v/6mDpbKIq3jmzJkDlUplvAUHW89UvkRERLVx9ezJsHYBcHO0vsGxV5m0oPj5+QEAcnNzq23Pzc013ufn54e8vLxq91dWVqKgoMC4z/VmzpwJtVptvGVmZpoyNhERkUUoKNVg41HDpw1jo0MlTlO/TFpQwsPD4efnhy1bthi3FRUVISEhATExMQCAmJgYFBYWIjEx0bjP1q1bodfrER0dXePzKpVKuLu7V7sRERHZmp8TM6HR6REVqEJUkErqOPWq1mNQSkpKkJqaavw6LS0Nhw4dgqenJ0JCQjB16lS88847aNasGcLDw/H6668jICDAeKVPy5YtMXDgQEycOBFLliyBVqvFlClTMHr06Du6goeIiMgW6fUC3++tGhwbbZ2XFl+r1gXlwIED6Nu3r/Hr6dOnAwDGjx+P5cuX4+WXX0ZpaSkmTZqEwsJC9OzZE5s2bYKjo6PxMStXrsSUKVPQr18/yOVyjBw5EgsWLDDByyEiIrJOO1Iu4lxBGdwd7TG8faDUcerdXc2DIhXOg0JERLbmqeX7sfVUHib0DMfr97eSOk6dSDYPChEREZleZkEZtiUbLjB5rJt1D469igWFiIjIzH2/NwNCAPc080a4t4vUcRoECwoREZEZK9fq8NMBw/Qa42LCpA3TgFhQiIiIzNiGIxdQWKZFoIcT7o30uf0DrAQLChERkRn7Lj4dgGHVYju5da67UxMWFCIiIjN1OLMQh7PUUNjJ8Uhn21rmhQWFiIjITH0bnwEAGNLWH16uSonTNCwWFCIiIjNUUKrB70eyAQCPx9jGpcXXYkEhIiIyQ6sPZEJTqUebQHd0CPaQOk6DY0EhIiIyMzq9wMoEw8c747qFQSazncGxV7GgEBERmZkdp/OQWXAFKicHDG1nmwvpsqAQERGZmauDYx/qFAQnhZ3EaaTBgkJERGRGMi6VYsfpiwBsZ92dmrCgEBERmZGr6+70bt4YYTay7k5NWFCIiIjMRJmmEj/tv7ruju2ePQFYUIiIiMzG2oPnUVReiVAvZ/RtYTvr7tSEBYWIiMgMCCGwfHc6AMOqxXIbWnenJiwoREREZmB36iWk5JXARWGHhzoHSR1HciwoREREZmD5njQAwKhOQXB3dJA4jfRYUIiIiCSWcakUW07lAQDGdw+TNoyZYEEhIiKS2Io9hkuL+7RojIjGrlLHMQssKERERBIqqajEmgOGS4uf4NkTIxYUIiIiCf2SmIXiikpENHZBr2aNpY5jNlhQiIiIJKLXCyzfkw7AcPbE1i8tvhYLChERkUR2pFxEWn4p3JT2GNmRlxZfiwWFiIhIIsuqJmZ7uEswXJT20oYxMywoREREEkjNK8HO0xchkwHjY8KkjmN2WFCIiIgksKJq7Em/SF+EeDlLG8YMsaAQERE1MPUVLX5JygIAPNkjTNowZooFhYiIqIGtOZCJMo0OzX1d0b2Jl9RxzBILChERUQPS6QVWxKcDAJ7oHg6ZjJcW14QFhYiIqAH9fTwHmQVX4OHsgAc6BEodx2yxoBARETWgr3YZVi1+LDoUTgo7idOYLxYUIiKiBpJ07jISMy5DYSfHuJhQqeOYNRYUIiKiBvJ11dmTYe0D4OPuKHEa88aCQkRE1AAyC8qw8egFAMCEnuESpzF/LChEREQNYPmedOgF0LOpN1r6u0sdx+yxoBAREdWzonItftqfCQD4zz08e3InWFCIiIjq2er9mSipqEQzH1f0bt5Y6jgWgQWFiIioHlXq9MZViyf05MRsd4oFhYiIqB5tPJaD84VX4OWiwAhOzHbHWFCIiIjqiRACX/17FgDweEwoHB04MdudYkG5Rm5ROeZuPIXtyXlSRyEiIiuQmHEZh7PUUNjL8Vg3TsxWGywo11i+Jx1LdpzBZ1tTpY5CRERW4Kt/DROzjewYCG9XpcRpLAsLyjWe6B4GhZ0cBzIuIzGjQOo4RERkwTIuleKvEzkAgKd68NLi2jJ5QdHpdHj99dcRHh4OJycnNGnSBG+//TaEEMZ9hBCYNWsW/P394eTkhNjYWKSkpJg6Sq35ujtiRIcAAMDSHWclTkNERJZs2e50CAH0adEYzXzdpI5jcUxeUN5//30sXrwYn332GU6ePIn3338f8+bNw8KFC437zJs3DwsWLMCSJUuQkJAAFxcXDBgwAOXl5aaOU2uTekUAADafzMWZiyUSpyEiIktUWKbB6gNVE7P1jJA4jWUyeUHZs2cPhg8fjiFDhiAsLAyjRo1C//79sW/fPgCGsyeffPIJ/vvf/2L48OFo27Ytvv32W2RnZ2PdunWmjlNrTX3cENvSB0LAOPKaiIioNr6Lz0CZRodW/u7o0dRL6jgWyeQFpXv37tiyZQtOnz4NADh8+DB27dqFQYMGAQDS0tKQk5OD2NhY42NUKhWio6MRHx9f43NWVFSgqKio2q0+Te7dBADwS9J55BVLf1aHiIgsR7lWh+V70gEAk3tHcGK2OjJ5QXn11VcxevRoREZGwsHBAR06dMDUqVMxduxYAEBOjmHAkK+vb7XH+fr6Gu+73pw5c6BSqYy34OBgU8eupnNoI3QI8YCmUo8VVT9kREREd+LnxCxcKtUg0MMJQ6L8pY5jsUxeUFavXo2VK1di1apVSEpKwooVKzB//nysWLGizs85c+ZMqNVq4y0zM9OEiW8kk8kwuZfhLMp38Rkorais1+9HRETWQacX+LJqeMDEe8Jhb8eLZevK3tRP+NJLLxnPogBAVFQUMjIyMGfOHIwfPx5+fn4AgNzcXPj7/69Z5ubmon379jU+p1KphFLZsNeP39fKF+HeLkjLL8WP+zMxoScvESMiolvbdCwHGZfK0MjZAQ93qd+z/dbO5NWurKwMcnn1p7Wzs4NerwcAhIeHw8/PD1u2bDHeX1RUhISEBMTExJg6Tp3ZyWWYeI9h5PU3u9Kg1eklTkREROZMCIElO84AAMbFhMFZYfJzADbF5AVl6NChePfdd/HHH38gPT0da9euxUcffYQHHngAgOHjk6lTp+Kdd97Bb7/9hqNHj2LcuHEICAjAiBEjTB3nrjzYMRDergqcL7yCP45ckDoOERGZsfizl3D0vBqODnKMi+G09nfL5PVu4cKFeP311/Hss88iLy8PAQEBmDx5MmbNmmXc5+WXX0ZpaSkmTZqEwsJC9OzZE5s2bYKjo6Op49wVRwc7PNE9DPP/Po2lO89iePsAjsYmIqIaLama4PPhzsHw4rT2d00mrp3i1UIUFRVBpVJBrVbD3d29Xr9XYZkG3eduRZlGh2+f6opezRvX6/cjIiLLcyK7CIMX/Au5DNj+Yl+EeDlLHcks1eb9m8OLb8PDWYFHqgY6fbGTE7cREdGNvthpGHsypG0Ay4mJsKDcgQk9w2Enl2FXaj6OnVdLHYeIiMxIZkEZfq8apzi5F6e1NxUWlDsQ1MgZ97c1XBK9lGdRiIjoGl/vSoNOL9CzqTfaBKqkjmM1WFDu0NWJ2/44ko30/FKJ0xARkTm4XKrBT/sNk4dO7s2zJ6bEgnKHWgW4o2+LxtALYGnVZ41ERGTbvtubgStaHVoHuKNnU2+p41gVFpRaiOvbFIBhnYUcNRcRJCKyZWWaSuOigJN6cVFAU2NBqYXOYZ7oGu4JrU7gq385FoWIyJb9sC8TBaUahHg6c1HAesCCUkvP9jGMRVmZcA6XSzUSpyEiIilUVOqMlxY/06cJFwWsB/wvWku9mzdG6wB3XNHqsKzq1B4REdmWX5POI7eoAn7ujniwY6DUcawSC0otyWQy41iU5bvTUFJRKXEiIiJqSJU6PRZvN5w9mdgrAkp7O4kTWScWlDoY0NoPEY1dUFReiVUJGVLHISKiBrThyAWcKyiDp4sCY7oGSx3HarGg1IGdXIanexvGonz5bxrKtTqJExERUUPQ6wU+354KAHiqRxicFSZfc5eqsKDU0Yj2gQhQOeJicQV+TsySOg4RETWAzSdzcTq3BG5KezweEyZ1HKvGglJHCns5JlWtubB05xlU6vQSJyIiovokhMDn2wxnTx6PCYXKyUHiRNaNBeUuPNIlBF4uCmQWXMGGqoWiiIjIOu1KzcfhLDUcHeR4qme41HGsHgvKXXBS2Bl/SD/fngq9XkiciIiI6suiqrMno7uEwNtVKXEa68eCcpcejwmFm9Iep3NL8M/JXKnjEBFRPUjMKMDeswVwsJMZP96n+sWCcpfcHR3weEwoAOCzbakQgmdRiIiszaJthnlPHuwQhAAPJ4nT2AYWFBOY0DMcTg52OJKlxvbTF6WOQ0REJnQ8W42tp/IglwFPVy13QvWPBcUEvFyVxrMon/6TwrMoRERW5OrYkyFtAxDu7SJxGtvBgmIiE++JgNJejkOZhfg3JV/qOEREZALJOcX482gOACCuL8+eNCQWFBNp7KbE2OiqsyhbeBaFiMgaLNiaAgAY1MYPkX7uEqexLSwoJjS5dwQU9nIkZlzGnjOXpI5DRER3ISW3GH8eNcxx9Xy/ZhKnsT0sKCbk6+6IR7uGADCcRSEiIsu1YGsqhAAGtPZFS3+ePWloLCgmNrl3BBR2cuxLK8DeszyLQkRkiVLzirHhSDYAnj2RCguKifmrnPBwlyAAhit6iIjI8iysOntyXytftA5QSR3HJrGg1INn+jSFg50M8WcvYV9agdRxiIioFs5cLMHvhw1nT17g2RPJsKDUg0APJ4zqFAwAWLiVZ1GIiCzJoq2p0AsgtqUP2gTy7IlUWFDqybN9msBeLsO/KflIzLgsdRwiIroDafmlWHfoPADghX7NJU5j21hQ6kmwpzMe7BgIAFjAK3qIiCzCZ1VnT+6N9EFUEM+eSIkFpR7F9W0KO7kMO05fxKHMQqnjEBHRLWRcuvbsCceeSI0FpR6FerlgRHvDWZSPN5+WOA0REd3KZ1tTodML9GnRGO2CPaSOY/NYUOrZc/f+7yxKYgav6CEiMkfnLpXh14M8e2JOWFDqWZi3C0Z1NMyL8uHfPItCRGSOFmxNgU4v0Kt5Y3QIaSR1HAILSoN4rp9hXpQ9Zy4hnmv0EBGZlTMXS/BrUhYAYPp9vHLHXLCgNICgRs4Y3cWwRs9Hm5O50jERkRn55J+UqnlPfNGeY0/MBgtKA4nr2xQKezn2p1/Gvyn5UschIiIAp3KKjLPG8uyJeWFBaSB+Kkc8Fh0KAPhw82meRSEiMgNXr7Ac0tYfrQK4YrE5YUFpQM/0aQInBzsczizE1lN5UschIrJpR7PU+Ot4LuQyYFosr9wxNywoDaixmxLjuhvOony0+TT0ep5FISKSyoebkwEAI9oHoqmPm8Rp6HosKA1scq8mcFHY4Xh2Ef46niN1HCIim5SYUYDtyRdhJ5fhBZ49MUssKA3M00WBp3qGAwA+/uc0dDyLQkTU4K7OS/Vw5yCEerlInIZqwoIigf/0jICboz1O55Zgw5FsqeMQEdmUPan52HPmEhR2cky5l2dPzBULigRUzg6YeE8EAODTf1JQqdNLnIiIyDYIIfBh1ZU7Y7oGI9DDSeJEdDP1UlDOnz+Pxx57DF5eXnByckJUVBQOHDhgvF8IgVmzZsHf3x9OTk6IjY1FSkpKfUQxW0/2CEMjZweczS81rv9ARET1a/vpi0jMuAylvRxxfZtKHYduweQF5fLly+jRowccHBywceNGnDhxAh9++CEaNfrf2gbz5s3DggULsGTJEiQkJMDFxQUDBgxAeXm5qeOYLTdHBzzbx/DL8cnm0yjX6iRORERk3YQQ+Khq7Mn47mHwcXeUOBHdir2pn/D9999HcHAwli1bZtwWHh5u/P9CCHzyySf473//i+HDhwMAvv32W/j6+mLdunUYPXq0qSOZrcdjQvHN7jRkq8vx/d4M/KfqYx8iIjK9P4/m4Oh5NVwUdpjci39vzZ3Jz6D89ttv6Ny5Mx566CH4+PigQ4cO+PLLL433p6WlIScnB7GxscZtKpUK0dHRiI+PN3Ucs+boYIepVZe3LdqWiqJyrcSJiIisk1anx/y/DfOe/OeeCHi5KiVORLdj8oJy9uxZLF68GM2aNcNff/2FZ555Bs8//zxWrFgBAMjJMcz94evrW+1xvr6+xvuuV1FRgaKiomo3azGyYxCaNHbB5TItvtp5Vuo4RERWafWBTKTll8LLRYGJPHtiEUxeUPR6PTp27Ij33nsPHTp0wKRJkzBx4kQsWbKkzs85Z84cqFQq4y04ONiEiaVlbyfHSwNaAAC+2pWGi8UVEiciIrIuVzQ6fPqP4UKMKfc2havS5KMbqB6YvKD4+/ujVatW1ba1bNkS586dAwD4+fkBAHJzc6vtk5uba7zvejNnzoRarTbeMjMzTR1bUgNa+6FdkAplGh0WbUuVOg4RkVVZticNecUVCGrkhEejQ6SOQ3fI5AWlR48eSE5Orrbt9OnTCA01rEETHh4OPz8/bNmyxXh/UVEREhISEBMTU+NzKpVKuLu7V7tZE5lMhlcGRgIAViZkILOgTOJERETWobBMg8XbzwAAZvRvDqW9ncSJ6E6ZvKBMmzYNe/fuxXvvvYfU1FSsWrUKX3zxBeLi4gAY3oynTp2Kd955B7/99huOHj2KcePGISAgACNGjDB1HIvRvak37mnmDa1O4KOqSYSIiOjuLN5+BsXllYj0c8PwdoFSx6FaMHlB6dKlC9auXYsffvgBbdq0wdtvv41PPvkEY8eONe7z8ssv47nnnsOkSZPQpUsXlJSUYNOmTXB0tO1r0l8eYDiLsu7QeZy8YD0DgYmIpHBBfQXL96QDAF4ZGAm5XCZtIKoVmRDC4larKyoqgkqlglqttrqPe+JWJeGPIxfQL9IHXz/RReo4REQW65Wfj+CnA5noGuaJnyZ3g0zGgiK12rx/cy0eMzPjvuawk8uw5VQe9qcXSB2HiMgipeaVYE2i4YKKVwZFspxYIBYUMxPR2BUPdzZcRj134ylY4AkuIiLJzf8rGXoB3NfKF51CG93+AWR2WFDM0Av9msHRQY7EjMv463jNk9cREVHNDp67jE3HcyCXAS9XzTNFlocFxQz5qRwxsWpdnrkbT0FTqZc4ERGRZRBCYM6fpwAYZupu5usmcSKqKxYUMzW5dxN4uyqQfqkMqxIypI5DRGQR/j6Ri33pBXB0kGN6/+ZSx6G7wIJiplyV9pgaa/jl+nRLChcSJCK6Da1Oj7kbDWdPJt4TAX+Vk8SJ6G6woJix0V2CjQsJfr7tjNRxiIjM2sq9GUjLL4W3qwKTezeROg7dJRYUM2ZvJ8fMQS0BAN/sTkPWZU6BT0RUE/UVLT7dYlgQcNp9zbkgoBVgQTFz/Vr6oFuEJzSVenz4N6fAJyKqyefbU3G5TIumPq54pLP1rHhvy1hQzJxMJsNrgw2rQ689eB5Hs9QSJyIiMi+ZBWVYtjsdAPB/gyNhb8e3NmvAo2gBooJUGNE+AADw7p8nOHkbEdE15v+dDE2lHt2beKFvCx+p45CJsKBYiBcHtIDCXo69Zwuw9VSe1HGIiMzCocxCrD+UDZkMeG1IS05pb0VYUCxEUCNnPNkjDAAwZ+MpVOo4eRsR2TYhBN774yQA4MEOQWgdoJI4EZkSC4oFebZPUzRydkBqXgl+2J8pdRwiIkldnZRNaS/HiwM4KZu1YUGxIConB+PkbR/9nQx1GSdvIyLbpKnkpGzWjgXFwoyNDkEzH1dcLtPiky287JiIbNOKPelVk7IpMbl3hNRxqB6woFgYezs5Zg01XHb8XXwGUvNKJE5ERNSwLhZXYEHVpGwvD2wBN0cHiRNRfWBBsUD3NGuM2JY+qNQLvPPHCanjEBE1qA//TkZxRSWiAlUY1TFI6jhUT1hQLNRrQ1rBwU6G7ckXsY2XHRORjTh2Xo2fDhguEnhjWCvI5bys2FqxoFiocG8XPNkjHADw9h8noOVlx0Rk5YQQePP34xACGN4+AJ1CPaWORPWIBcWCTbm3KbxdFTh7sRTfxmdIHYeIqF79cfQC9qdfhpODHV4dFCl1HKpnLCgWzN3RAS/2bwEA+OSf07hUUiFxIiKi+nFFo8OcPw2XFT/TpwkvK7YBLCgW7qHOwWjl747i8kp8tJmXHRORdfpi51mcL7yCQA8nTOrFy4ptAQuKhbOTyzC76rLjH/adw8kLRRInIiIyrezCK1i8IxUA8H+DW8LRwU7iRNQQWFCsQHSEF4ZE+UMvUDWAjKsdE5H1mLvxFMq1enQN98TgKD+p41ADYUGxEjMHR0JZtdrx70cuSB2HiMgk9qUV4LfDhtWKZ93fiqsV2xAWFCsR1MgZU/o2BQC8+8cJlFRUSpyIiOjuVOr0mLX+GABgdJcQtAnkasW2hAXFikzsFYFQL2fkFv1vGmgiIku1Ij4Dp3KK4eHsgJcHtJA6DjUwFhQr4uhghzeGtQYAfLMrDSm5xRInIiKqm7yicnxcdWXiKwMj0chFIXEiamgsKFambwsf3NfKF5V6gVnrOWCWiCzTe3+eRElFJdoFe+CRzsFSxyEJsKBYoVn3t4LSXo74s5c4YJaILM7es5ew7pBhYOw7w9twvR0bxYJihYI9nRHHAbNEZIG01wyMHRsdgqggDoy1VSwoVmoSB8wSkQVasScdp3NL4OmiMC7lQbaJBcVKOTrY4Y2hHDBLRJYj95qBsa8OjISHMwfG2jIWFCvWN9IHsS05YJaILMO7f5xEqUaHDiEeGNUpSOo4JDEWFCs3e+j/BsyuP5QtdRwiohrtOZOP3w5nQy4D3ubAWAILitUL9nTG8/2aAQDe3nAChWUaiRMREVVXrtXhv2sNA2Mf6xbKGWMJAAuKTZh4TwSa+bjiUqkGczeekjoOEVE1i7efwdn8UjR2U+JFzhhLVVhQbIDCXo73HowCAPy4PxP70gokTkREZJCaV4LF288AAN4Y2hrujg4SJyJzwYJiI7qEeWJMV8NsjP+39ig0lXqJExGRrRNCGP4e6fS4N9IHg6P8pI5EZoQFxYa8MjAS3q4KpOaV4IudZ6SOQ0Q2bs2BLOxLK4CTgx3eHNYaMhkHxtL/sKDYEA9nBV6/vxUAYMHWVKTnl0qciIhsVX5JBd798yQAYPp9zRHs6SxxIjI3LCg2Zli7ANzTzBuaSj1eW3eUc6MQkSTe/eMk1Fe0aOXvjid7hEkdh8wQC4qNkclkeGdEGyjt5didegnrDp2XOhIR2ZhdKflYe/A8ZDJgzoNRsLfjWxHdiD8VNijUy+WauVFO4nIp50YhooZRrtXhv+uOAgDGx4ShXbCHtIHIbNV7QZk7dy5kMhmmTp1q3FZeXo64uDh4eXnB1dUVI0eORG5ubn1HoWtMvCcCzX1dUVCqwTt/nJQ6DhHZiIVbU5B+qQx+7o6Y0b+51HHIjNVrQdm/fz+WLl2Ktm3bVts+bdo0/P7771izZg127NiB7OxsPPjgg/UZha6jsJdj7si2kMmAX5KysC05T+pIRGTljp1XY8mOswCAN4a1hhvnPKFbqLeCUlJSgrFjx+LLL79Eo0aNjNvVajW+/vprfPTRR7j33nvRqVMnLFu2DHv27MHevXvrKw7VoGNIIzzVIxwA8NqvR1FcrpU4ERFZK61Oj5d/PgKdXmBIlD8GtuGcJ3Rr9VZQ4uLiMGTIEMTGxlbbnpiYCK1WW217ZGQkQkJCEB8fX+NzVVRUoKioqNqNTOPF/i0Q4umMbHU53t/EafCJqH4s3XEGJy4UoZGzA94Y1lrqOGQB6qWg/Pjjj0hKSsKcOXNuuC8nJwcKhQIeHh7Vtvv6+iInJ6fG55szZw5UKpXxFhwcXB+xbZKTwg5zRxqmwf9+7znsPXtJ4kREZG1ScouxYEsqAGD20NZo7KaUOBFZApMXlMzMTLzwwgtYuXIlHB0dTfKcM2fOhFqtNt4yMzNN8rxk0L2JN8Z0DQEAvPLLEVzR6CRORETWQqcXeOnnI9Do9OgX6YPh7QOkjkQWwuQFJTExEXl5eejYsSPs7e1hb2+PHTt2YMGCBbC3t4evry80Gg0KCwurPS43Nxd+fjV/JqlUKuHu7l7tRqY1c3Ak/FWOyLhUho82J0sdh4isxLLdaTiUWQg3pT3efSCK09nTHTN5QenXrx+OHj2KQ4cOGW+dO3fG2LFjjf/fwcEBW7ZsMT4mOTkZ586dQ0xMjKnj0B1yd3TAew8YPur5epfhDwoR0d1Izy/FB38Z/sHz2pCW8FOZ5qw62QZ7Uz+hm5sb2rRpU22bi4sLvLy8jNsnTJiA6dOnw9PTE+7u7njuuecQExODbt26mToO1ULfSB880CEQaw+ex8s/H8bvz/WE0t5O6lhEZIH0eoFXfjmCiko9ejT1wiNdOHaQakeSmWQ//vhj3H///Rg5ciR69eoFPz8//Prrr1JEoevMur8VvF0VOJ1bgoVVg9qIiGpr5b5zSKhaqXjug2350Q7VmkxY4GpxRUVFUKlUUKvVHI9SDzYevYBnVibBTi7DL890R3tORU1EtZBxqRSDPv0XZRodZg9thSer5lsiqs37N9fioRsMivLH8PYB0OkFpq8+hHItr+ohojuj0wvMWH0YZRodukV4YnxMmNSRyEKxoFCN3hrWBr7uSpy9WMoJ3Ijojn3171kcyLgMV6U9PhjVDnI5P9qhumFBoRqpnB0wd6RhDaVlu9Ox50y+xImIyNwl5xTjw79PAwBev78lgj2dJU5ElowFhW6qbwsf4wRuL605wrV6iOimNJV6TPvpEDQ6Pe6N9MHDnXnVDt0dFhS6pdeGtESwpxPOF17BOxtOSh2HiMzUwq0pxrV25o7khGx091hQ6JZclfaYP6odZDLgpwOZ2HoqV+pIRGRmDmUW4vPtZwAA74yIgo8bJ2Sju8eCQrcVHeGFCVWXCb7yy1FcLtVInIiIzEW5Vofpqw9BpxcY1i4AQ9r6Sx2JrAQLCt2RFwe0QFMfV1wsrsD/rT0KC5w+h4jqwdyNp3D2Yil83JR4a3hrqeOQFWFBoTvi6GCHjx9uD3u5DBuP5WD1Aa4oTWTrtp3Kw/I96QCAeaPawsNZIW0gsiosKHTHooJUmNG/BQDgjd9O4OzFEokTEZFULhZX4KWfDwMAnugehj4tfCRORNaGBYVqZXKvCMREeOGKVocXfjwETaVe6khE1MCEEHjp58PIL9Eg0s8Nrw6KlDoSWSEWFKoVuVyGjx5pB5WTA46eV+Pjf05LHYmIGtjyPenYnnwRCns5Ph3dAY4OXPWcTI8FhWrNX+WE90dGAQCW7DjDWWaJbMipnCLM2WhY/uK1wS3Rws9N4kRkrVhQqE4GtvHH6C7BEAKY/tNhFJbx0mMia1eu1eH5Hw5CU2mYLXZcTKjUkciKsaBQnc0a2goR3i7IKSrHzF956TGRtXvvz5M4nVsCb1cl5o1qy9liqV6xoFCdOSvs8enoDnCwM1x6/ON+XnpMZK22nMzFt/EZAID5D7WFt6tS4kRk7VhQ6K5EBanwovHS4+M4lVMkcSIiMrXzhVcwY43hkuIne/CSYmoYLCh01ybeE4E+LRqjolKPZ1cmobSiUupIRGQiWp0ez61KQmGZFm2DVLykmBoMCwrdNblcho8ebg8/d0ecvViK/647xvEoRFZi/l/JSDpXCDdHe3w2piOU9rykmBoGCwqZhKeLAgsf7QA7uQxrD57nVPhEVmDLyVws3XkWAPDBqLYI8XKWOBHZEhYUMpkuYZ6Y0b85AGDWeo5HIbJk1447eaJ7GAa24SrF1LBYUMiknu7VBL2bczwKkSW7ftzJzMEcd0INjwWFTMowHqUdx6MQWbD5f3PcCUmPBYVMzstVWW08yqp956SORER3aPOJXCzdwXEnJD0WFKoXXcI8q82PcvDcZYkTEdHtnL1Yguk/HQLAcSckPRYUqjdP947AgNa+0OoEnvk+CfklFVJHIqKbKK2oxNPfJ6K4ohJdwhrhtSEtpY5ENo4FheqNTCbD/IfaIaKxYb2eKauSUKnTSx2LiK4jhMArvxzB6dwSNHZTYtGjHeFgx7cHkhZ/AqleuTk6YOljneCisMPeswV4f9MpqSMR0XW+3pWGDUcuwF4uw+KxHeHj7ih1JCIWFKp/zXzd8MFD7QAAX/6bhg1HsiVORERXxZ+5hDkbDf9weP3+Vugc5ilxIiIDFhRqEIOj/DG5dwQA4OWfj+B0brHEiYjogvoKnvshCTq9wAMdAjEuJlTqSERGLCjUYF7q3wLdm3ihTKPD098lQn1FK3UkIptVrtVVDV7XoKW/O957IAoymUzqWERGLCjUYOzt5Fg4pgMCVI44m1+K5344yEGzRBIQQuD/fj2KQ5mFUDk5YMljHeGk4GRsZF5YUKhBebkq8cW4znBysMPO0xfx3p8cNEvU0JbuPItfD56HnVyGz8d2RKiXi9SRiG7AgkINrk2gCh8+bBg0+83uNPzImWaJGsyWk7nGq+lmD22FHk29JU5EVDMWFJLE4Ch/TIs1rHz8+vpjSDh7SeJERNbvdG4xnv/hIIQAHo0OwePdOCiWzBcLCknm+X5NMaStv2Gm2ZVJyCwokzoSkdUqKNXgPysOoFSjQ7cIT7w5rDUHxZJZY0EhychkMswf1Q5tAt2NfzxLKiqljkVkdbQ6PZ5dmYhzBWUI9nTC52M7caZYMnv8CSVJOSns8OW4zmjspkRybjFe+OEgdHohdSwiqyGEwH/XHsPeswVwUdjh6/Fd4OmikDoW0W2xoJDk/FVO+OLxTlDYy7HlVB7e/P04hGBJITKFz7efwU8HMiGXAQvGdEBzXzepIxHdERYUMgsdQhrhk0faQyYDvo3PwFf/pkkdicjirT90Hh/8lQwAeGNYa/Rr6StxIqI7x4JCZmNwlD9eG2xY4v3dP0/iz6MXJE5EZLn2nr2El9YcAQBMvCcc42LCpA1EVEssKGRWJvQMx/iq9UCm/nQIiRkFEicisjypecWY9O0BaHR6DGrjh5mDWkodiajWWFDIrMhkMswa2hqxLX2hqdTjPysOIC2/VOpYRBbjYnEFnli2H0XllegY4oGPH2kPuZyXE5PlYUEhs2Mnl2HBmPZoG6TC5TItnly2D5dKKqSORWT2Sisq8Z8V+5F1+QpCvZzx5bjOcHTgGjtkmVhQyCw5K+zx9fguCGrkhPRLZXhy+X7OkUJ0C5pKPZ7+PhGHs9Ro5OyA5U92hZerUupYRHVm8oIyZ84cdOnSBW5ubvDx8cGIESOQnJxcbZ/y8nLExcXBy8sLrq6uGDlyJHJzc00dhSxcYzclVjzVFZ4uChzJUmPStwdQUamTOhaR2dHpBaavPoR/U/LhrLDDN090Qbg3FwAky2bygrJjxw7ExcVh79692Lx5M7RaLfr374/S0v+NI5g2bRp+//13rFmzBjt27EB2djYefPBBU0chK9CksSuWP9kFLgo77DlzCVN/PMSJ3IiuIYTAG78dx4YjF+BgJ8OSxzqhQ0gjqWMR3TWZqOcZsS5evAgfHx/s2LEDvXr1glqtRuPGjbFq1SqMGjUKAHDq1Cm0bNkS8fHx6Nat222fs6ioCCqVCmq1Gu7u7vUZn8zEntR8PLFsPzQ6PcZ0DcZ7D0RxHREiAB9vPo1Pt6RAJgMWjO6Aoe0CpI5EdFO1ef+u9zEoarUaAODp6QkASExMhFarRWxsrHGfyMhIhISEID4+vsbnqKioQFFRUbUb2ZbuTb2xYEx7yGXAD/syjZNPEdmy5bvT8OmWFADAW8PbsJyQVanXgqLX6zF16lT06NEDbdq0AQDk5ORAoVDAw8Oj2r6+vr7Iycmp8XnmzJkDlUplvAUHB9dnbDJTA9v4470HogAYpu/+cudZiRMRSWfdwfN4c8MJAMC02OZ4vFuoxImITKteC0pcXByOHTuGH3/88a6eZ+bMmVCr1cZbZmamiRKSpRndNQSvDIwEYJht9rv4dGkDEUngjyMXMH31IQgBjI8JxfP9mkodicjk7OvriadMmYINGzZg586dCAoKMm738/ODRqNBYWFhtbMoubm58PPzq/G5lEollEpeLkcGT/eOQFG5Fou3n8Hr64/DwU6O0V1DpI5F1CD+Pp6DF348CL0AHu4chNlDW3M8Flklk59BEUJgypQpWLt2LbZu3Yrw8PBq93fq1AkODg7YsmWLcVtycjLOnTuHmJgYU8chKySTyfDygBaY0NPwszVz7VH8kpglcSqi+rctOQ9xq5JQqRcY0T4Acx5sy1liyWqZ/AxKXFwcVq1ahfXr18PNzc04rkSlUsHJyQkqlQoTJkzA9OnT4enpCXd3dzz33HOIiYm5oyt4iABDSfnvkJbQ6vT4Nj4DL/18GA72cgzjIEGyUrtT8/H0d4nQ6gQGR/lh/kPtYMdyQlbM5JcZ3+xU47Jly/DEE08AMEzUNmPGDPzwww+oqKjAgAED8Pnnn9/0I57r8TJjukqvF3ht3VH8sC8TdnIZPhvTAYOi/KWORWRS+9IKMP6bfbii1SG2pS8WP9YRDnacCJwsT23ev+t9HpT6wIJC19LrBV7+5Qh+TsyCvVyGzx7tgIFtWFLIOsSfuYQJK/ajTKND7+aN8cW4TlDac30dskxmNQ8KUX2Ty2V4f2RbDG8fgEq9QNyqg/jtcLbUsYju2r8pF/Hk8n0o0+hwTzNvLH2c5YRsBwsKWQU7uQwfPdweD3YMhE4vMPXHg/iZA2fJgm09lYsJKw6gXKvHvZE+XJmYbA4LClkNO7kM80e1w5iuIdAL4MU1h7EyIUPqWES1tulYDiZ/lwhNpR4DWvtiyWOdWE7I5rCgkFWRy2V474E2eKJ7GADgtbXH8M2uNGlDEdXChiPZiFuVBK1O4P62/vjs0Y5Q2PNPNdke/tST1ZHJZJg9tBUm944AALy14QQWbUuFBY4HJxvz475zeP6Hg9DpBR7sGIhPR3fg1Tpks/iTT1ZJJpPh1YGReKFfMwDAB38l4+0NJ6HXs6SQ+RFCYNG2VLz661HoBTCmawjmj+I8J2Tb6m2qeyKpyWQyTLuvOdwc7fHOHyfxze40XCqtwAej2vGUOZkNvV4Yfz4BYErfppjRvzmnryebx4JCVu8/90TA21WJF9ccxvpD2bhcpsXisR3houSPP0lLq9PjpTWHse6Q4bL4Wfe3wlM9w2/zKCLbwH9Gkk0Y0SEQX43vDCcHO+w8fRGPfpWAglKN1LHIhpVpKjHx2wNYdygb9nIZPnmkPcsJ0TVYUMhm9Gnhg1UTo9HI2QGHMwsxavEepOeXSh2LbFBuUTkeXhqP7ckX4eggx5fjO2NEh0CpYxGZFRYUsikdQhphzdPdEejhhLP5pXjg893Yl1YgdSyyIScvFGHEot04dr4Ini4KrPxPN/Rt4SN1LCKzw4JCNqepjyvWPtsd7YJUuFymxWNfJWDtQc46S/VvW3IeRi3egwvqcjRp7IJ1z/ZAp9BGUsciMkssKGSTfNwd8eOkGAxq4weNTo9pPx3GR38nc64Uqjff7c3AhOX7UarRISbCC78+0wMhXs5SxyIyWywoZLOcFHZY9GhHPNOnCQBgwdZUPP/jIVzR6CRORtZEq9Nj9vpjeH3dMegFMKpTEFY81RUqZwepoxGZNRYUsmlyuQyvDIzEvJFtYS+X4ffD2Ri1ZA8yC8qkjkZWIL+kAmO/SsCKeMOaUC/2b44PRrXlPDxEd4C/JUQAHu4SjO8mRMPTRYHj2UUY9tku7ErJlzoWWbCjWWoMW7gL+9IK4Kq0xxePd8KUe5txAjaiO8SCQlQlpokXfn+uJ6ICDYNnx32TgC92nuG4FKq1XxKzMHLJHmSryxHh7YJ1cd3Rv7Wf1LGILAoLCtE1Aj2csObpGIzqFAS9AN778xSe++EgSioqpY5GFqBcq8Os9ccwY81haCr1uDfSB+um9EBTHzepoxFZHBYUous4Otjhg1Ft8fbw1rCXy7DhyAUMW7gLx7PVUkcjM5aeX4qRi/fg26rxJs/d2xRfjesMd0cOhiWqCxYUohrIZDI8HhOGHyd1g7/KsWpStz34Lj6dH/nQDX4/nI37F+7C8ewiNHJ2wLInumBG/xaQczViojqTCQv8a1tUVASVSgW1Wg13d3ep45CVu1yqwYtrDmPLqTwAwKA2fpg7si1UTvyXsa0r1+rw9oYTWJlwDgDQJawRFozpAH+Vk8TJiMxTbd6/eQaF6DYauSjw1fjOeP3+VnCwk2HjsRwMWfAv9qdzinxbduy8GkMX7jKWk2f7NMEPE7uxnBCZCM+gENXC4cxCTPkhCZkFVyCTAZPuicC0+5rD0cFO6mjUQHR6gSU7zuCTf05DqxPwdlXiw4fboXfzxlJHIzJ7tXn/ZkEhqqWici3e/v0E1iQa1u9p7uuKjx5ujzaBKomTUX3LuFSK6asPIzHjMgBgQGtfvPdAFLxclRInI7IMLChEDWDziVzM/PUI8ks0sJfL8EK/Zni6TxM42PGTU2uj1wt8n5CBuRtPoUyjg6vSHm8Ma42RHQM58RpRLbCgEDWQSyUVeG3tMWw6ngMAaOnvjjkPRqF9sIe0wchkUnKL8eqvR41nTbqGe+LDh9oh2JML/RHVFgsKUQMSQmDdofN48/cTKCzTQiYDxseE4cUBLeCqtJc6HtVRRaUOn287g8+3p0KrE3BR2OHlgZF4rFso7Hj5MFGdsKAQSeBSSQXe+eMk1h48DwDwVznizWGtOcW5BdpzJh+z1h9Hal4JAKBfpA/eHtEGAR68QofobrCgEEno35SLeG3tMZyrWhG5T4vG+O+QVmjq4ypxMrqd84VX8N4fJ/HH0QsAAG9XBd4Y1hpDovw51oTIBFhQiCR2RaPDgq0p+Orfs9DqBOzlMozvHobn+zXjBG9mqFyrw9IdZ7F4RyrKtXrIZcBj3UIx/b7m8HBWSB2PyGqwoBCZibT8Urz7xwn8c9IwC62niwIz+jfHI52DYc+rfSSn0wv8dvg85v91GucLrwAAosM98caw1mjpz78tRKbGgkJkZnacvoi3N5wwjmkI93bBtPua4/4of67XIgEhBLYl52HepmScyikGYBgz9H+DW+L+tvw4h6i+sKAQmSGtTo+VezOwcGsqLpVqAACRfm54aUAL3BvpwzfFBnIgvQDzNiVjX9VSBW6O9nimTxM82T0cTgrOCExUn1hQiMxYSUUllu1Kwxc7z6K4ohIA0D7YA3F9m6JfpA/PqNQDIQR2p17CZ9tSsPesoZgo7eV4okcYnundhONMiBoICwqRBSgs02DxjjNYsScd5Vo9AMO0+c/0aYKhbQM4RsUEhBDYcjIPn21LxaHMQgCAg50MozoF4/l+TbmwH1EDY0EhsiB5xeX4Zlc6vt+bgZKqMypBjZzwVI9wjOocBHdHXvVTW2WaSvyadB4r9qQjpWrcj9JejjFdQzCpVwTnMyGSCAsKkQVSX9Hi+70Z+GZXmnGMirPCDiM6BGJcTCgi/fizfjuZBWX4Nj4dP+3PRFG5oey5KOzweEwYJvQMR2M3LupHJCUWFCILVq7V4efELHwbn47TuSXG7V3DPfFI52AMbOMHF06hb1Su1eHvE7lYcyATu1LzcfUvWpiXM8Z3D8OoTkFw41koIrPAgkJkBYQQ2Hu2AN/tTcdfx3Oh0xt+VZ0VdhjYxg8jOwahW4SXTa4LI4TA4Sw1fknMwvpD541nSwDgnmbeeLJHGPo054BjInPDgkJkZXLU5Vh9IBO/JmUh/VKZcbufuyMGtPbFgNZ+6BruadUDa/V6gYOZhdh49AI2HssxTqwGAAEqR4zqFISRnYIQ6uUiYUoiuhUWFCIrJYRA0rlC/JKUhQ2Hs6udOfBwdkBsS1/0i/RB9ybeUDlb/scaxeVa7E69hJ0pF7H1ZB5yisqN9zkr7BDb0hcPdQ5C9ybeNnkmicjSsKAQ2YByrQ67U/Px1/Ec/HMyDwVVA2sBQC4DogJV6NHUGz2beqNdsIdFjFu5otHhcFYh9qcV4N+UfCSdu4xK/f/+RLkq7dGvpQ8GtfFH7+aNObEakYVhQSGyMZU6PfanX8bfJ3KwKyXfeGntVXIZ0MLPHe2DPdAhxANRgSpENHaB0l66N3hNpR5nLpYgOacYh7MKkZRxGcezi6oVEsCwLECvZt7o3aIxujfxhqMDSwmRpWJBIbJxuUXl2JWSj92p+Yg/ewkX1OU37COXAWFeLmjm64pmPm4I9nRCgIfhFujhZJIioKnUI7eoHJmXy5BVcAWZl8uQcakMp3OLkZpXckMZAQBfdyU6h3qiWxMv9G7WGCFeznedg4jMg8UUlEWLFuGDDz5ATk4O2rVrh4ULF6Jr1663fRwLClHt5KjLcSjzMg5mFuLguUKcvFCE4mvGr9TE3dEeHs4KqJwcjDeFvRz2chns7Qz/CwAVlTpoKvWoqNSjXKvD5TItLpdpUFCiMU7lfzNuSntE+ruhpb87OoU2QqfQRgj0cOK6RERWyiIKyk8//YRx48ZhyZIliI6OxieffII1a9YgOTkZPj4+t3wsCwrR3RFCIK+4Aqdzi3E6twSpeSXILrxivJVqdCb7Xgo7OYIaOSHI0xnBjZwQ7OmMZj6uiPR3R4DKkWWEyIZYREGJjo5Gly5d8NlnnwEA9Ho9goOD8dxzz+HVV1+95WNZUIjqjxACRVcqcbGkHOor2v/dyrTQ6gS0ej0qdQKVOsP6QUoHOyjt5VU3O6icHeDlokAjFwW8XBRwd3TgfCREBKB279+SDOvXaDRITEzEzJkzjdvkcjliY2MRHx9/w/4VFRWoqKgwfl1UVNQgOYlskUwmg8rZwSouUyYiyyXJrE75+fnQ6XTw9fWttt3X1xc5OTk37D9nzhyoVCrjLTg4uKGiEhERkQQsYtrJmTNnQq1WG2+ZmZlSRyIiIqJ6JMlHPN7e3rCzs0Nubm617bm5ufDz87thf6VSCaWSq5ASERHZCknOoCgUCnTq1AlbtmwxbtPr9diyZQtiYmKkiERERERmRLK5r6dPn47x48ejc+fO6Nq1Kz755BOUlpbiySeflCoSERERmQnJCsojjzyCixcvYtasWcjJyUH79u2xadOmGwbOEhERke3hVPdERETUIGrz/m0RV/EQERGRbWFBISIiIrPDgkJERERmhwWFiIiIzA4LChEREZkdFhQiIiIyO5LNg3I3rl4ZzVWNiYiILMfV9+07meHEIgtKcXExAHBVYyIiIgtUXFwMlUp1y30scqI2vV6P7OxsuLm5QSaTmfS5i4qKEBwcjMzMTKucBI6vz/JZ+2vk67N81v4arf31AfX3GoUQKC4uRkBAAOTyW48yscgzKHK5HEFBQfX6Pdzd3a32Bw/g67MG1v4a+fosn7W/Rmt/fUD9vMbbnTm5ioNkiYiIyOywoBAREZHZYUG5jlKpxOzZs6FUKqWOUi/4+iyftb9Gvj7LZ+2v0dpfH2Aer9EiB8kSERGRdeMZFCIiIjI7LChERERkdlhQiIiIyOywoBAREZHZsbmC8u6776J79+5wdnaGh4dHjfucO3cOQ4YMgbOzM3x8fPDSSy+hsrLyls9bUFCAsWPHwt3dHR4eHpgwYQJKSkrq4RXUzvbt2yGTyWq87d+//6aP69Onzw37P/300w2Y/M6FhYXdkHXu3Lm3fEx5eTni4uLg5eUFV1dXjBw5Erm5uQ2U+M6lp6djwoQJCA8Ph5OTE5o0aYLZs2dDo9Hc8nHmfvwWLVqEsLAwODo6Ijo6Gvv27bvl/mvWrEFkZCQcHR0RFRWFP//8s4GS1t6cOXPQpUsXuLm5wcfHByNGjEBycvItH7N8+fIbjpejo2MDJa6dN95444askZGRt3yMJR2/mv6eyGQyxMXF1bi/JRy7nTt3YujQoQgICIBMJsO6deuq3S+EwKxZs+Dv7w8nJyfExsYiJSXlts9b29/j2rK5gqLRaPDQQw/hmWeeqfF+nU6HIUOGQKPRYM+ePVixYgWWL1+OWbNm3fJ5x44di+PHj2Pz5s3YsGEDdu7ciUmTJtXHS6iV7t2748KFC9Vu//nPfxAeHo7OnTvf8rETJ06s9rh58+Y1UOrae+utt6plfe655265/7Rp0/D7779jzZo12LFjB7Kzs/Hggw82UNo7d+rUKej1eixduhTHjx/Hxx9/jCVLluD//u//bvtYcz1+P/30E6ZPn47Zs2cjKSkJ7dq1w4ABA5CXl1fj/nv27MGYMWMwYcIEHDx4ECNGjMCIESNw7NixBk5+Z3bs2IG4uDjs3bsXmzdvhlarRf/+/VFaWnrLx7m7u1c7XhkZGQ2UuPZat25dLeuuXbtuuq+lHb/9+/dXe22bN28GADz00EM3fYy5H7vS0lK0a9cOixYtqvH+efPmYcGCBViyZAkSEhLg4uKCAQMGoLy8/KbPWdvf4zoRNmrZsmVCpVLdsP3PP/8Ucrlc5OTkGLctXrxYuLu7i4qKihqf68SJEwKA2L9/v3Hbxo0bhUwmE+fPnzd59ruh0WhE48aNxVtvvXXL/Xr37i1eeOGFhgl1l0JDQ8XHH398x/sXFhYKBwcHsWbNGuO2kydPCgAiPj6+HhKa1rx580R4ePgt9zHn49e1a1cRFxdn/Fqn04mAgAAxZ86cGvd/+OGHxZAhQ6pti46OFpMnT67XnKaSl5cnAIgdO3bcdJ+b/T0yR7Nnzxbt2rW74/0t/fi98MILokmTJkKv19d4vyUdOyGEACDWrl1r/Fqv1ws/Pz/xwQcfGLcVFhYKpVIpfvjhh5s+T21/j+vC5s6g3E58fDyioqLg6+tr3DZgwAAUFRXh+PHjN32Mh4dHtTMSsbGxkMvlSEhIqPfMtfHbb7/h0qVLePLJJ2+778qVK+Ht7Y02bdpg5syZKCsra4CEdTN37lx4eXmhQ4cO+OCDD275kVxiYiK0Wi1iY2ON2yIjIxESEoL4+PiGiHtX1Go1PD09b7ufOR4/jUaDxMTEav/t5XI5YmNjb/rfPj4+vtr+gOF30hKOFWA4XgBue8xKSkoQGhqK4OBgDB8+/KZ/b8xBSkoKAgICEBERgbFjx+LcuXM33deSj59Go8H333+Pp5566pYL01rSsbteWloacnJyqh0jlUqF6Ojomx6juvwe14VFLhZYn3JycqqVEwDGr3Nycm76GB8fn2rb7O3t4enpedPHSOXrr7/GgAEDbrvY4qOPPorQ0FAEBATgyJEjeOWVV5CcnIxff/21gZLeueeffx4dO3aEp6cn9uzZg5kzZ+LChQv46KOPatw/JycHCoXihjFIvr6+Zne8rpeamoqFCxdi/vz5t9zPXI9ffn4+dDpdjb9jp06dqvExN/udNPdjBRhWXp86dSp69OiBNm3a3HS/Fi1a4JtvvkHbtm2hVqsxf/58dO/eHcePH6/3hVFrKzo6GsuXL0eLFi1w4cIFvPnmm7jnnntw7NgxuLm53bC/JR+/devWobCwEE888cRN97GkY1eTq8ehNseoLr/HdWEVBeXVV1/F+++/f8t9Tp48eduBXJakLq85KysLf/31F1avXn3b5792/ExUVBT8/f3Rr18/nDlzBk2aNKl78DtUm9c3ffp047a2bdtCoVBg8uTJmDNnjtlORV2X43f+/HkMHDgQDz30ECZOnHjLx0p9/MggLi4Ox44du+UYDQCIiYlBTEyM8evu3bujZcuWWLp0Kd5+++36jlkrgwYNMv7/tm3bIjo6GqGhoVi9ejUmTJggYTLT+/rrrzFo0CAEBATcdB9LOnaWxioKyowZM27ZcAEgIiLijp7Lz8/vhpHIV6/u8PPzu+ljrh8YVFlZiYKCgps+5m7V5TUvW7YMXl5eGDZsWK2/X3R0NADDv+Ab4g3ubo5pdHQ0KisrkZ6ejhYtWtxwv5+fHzQaDQoLC6udRcnNza2343W92r6+7Oxs9O3bF927d8cXX3xR6+/X0MfvZry9vWFnZ3fDFVO3+m/v5+dXq/3NxZQpU4wD5mv7L2kHBwd06NABqamp9ZTOdDw8PNC8efObZrXU45eRkYF//vmn1mcdLenYAf97X8vNzYW/v79xe25uLtq3b1/jY+rye1wnJhvNYmFuN0g2NzfXuG3p0qXC3d1dlJeX1/hcVwfJHjhwwLjtr7/+MqtBsnq9XoSHh4sZM2bU6fG7du0SAMThw4dNnMz0vv/+eyGXy0VBQUGN918dJPvzzz8bt506dcpsB8lmZWWJZs2aidGjR4vKyso6PYc5Hb+uXbuKKVOmGL/W6XQiMDDwloNk77///mrbYmJizHaQpV6vF3FxcSIgIECcPn26Ts9RWVkpWrRoIaZNm2bidKZXXFwsGjVqJD799NMa77e043fV7NmzhZ+fn9BqtbV6nLkfO9xkkOz8+fON29Rq9R0Nkq3N73GdsprsmSxERkaGOHjwoHjzzTeFq6urOHjwoDh48KAoLi4WQhh+uNq0aSP69+8vDh06JDZt2iQaN24sZs6caXyOhIQE0aJFC5GVlWXcNnDgQNGhQweRkJAgdu3aJZo1aybGjBnT4K/vZv755x8BQJw8efKG+7KyskSLFi1EQkKCEEKI1NRU8dZbb4kDBw6ItLQ0sX79ehERESF69erV0LFva8+ePeLjjz8Whw4dEmfOnBHff/+9aNy4sRg3bpxxn+tfnxBCPP300yIkJERs3bpVHDhwQMTExIiYmBgpXsItZWVliaZNm4p+/fqJrKwsceHCBePt2n0s6fj9+OOPQqlUiuXLl4sTJ06ISZMmCQ8PD+OVc48//rh49dVXjfvv3r1b2Nvbi/nz54uTJ0+K2bNnCwcHB3H06FGpXsItPfPMM0KlUont27dXO15lZWXGfa5/jW+++ab466+/xJkzZ0RiYqIYPXq0cHR0FMePH5fiJdzSjBkzxPbt20VaWprYvXu3iI2NFd7e3iIvL08IYfnHTwjDm21ISIh45ZVXbrjPEo9dcXGx8b0OgPjoo4/EwYMHRUZGhhBCiLlz5woPDw+xfv16ceTIETF8+HARHh4urly5YnyOe++9VyxcuND49e1+j03B5grK+PHjBYAbbtu2bTPuk56eLgYNGiScnJyEt7e3mDFjRrUWvW3bNgFApKWlGbddunRJjBkzRri6ugp3d3fx5JNPGkuPORgzZozo3r17jfelpaVV+29w7tw50atXL+Hp6SmUSqVo2rSpeOmll4RarW7AxHcmMTFRREdHC5VKJRwdHUXLli3Fe++9V+1s1/WvTwghrly5Ip599lnRqFEj4ezsLB544IFqb/rmYtmyZTX+vF578tMSj9/ChQtFSEiIUCgUomvXrmLv3r3G+3r37i3Gjx9fbf/Vq1eL5s2bC4VCIVq3bi3++OOPBk585252vJYtW2bc5/rXOHXqVON/D19fXzF48GCRlJTU8OHvwCOPPCL8/f2FQqEQgYGB4pFHHhGpqanG+y39+AlhOAMOQCQnJ99wnyUeu6vvWdffrr4OvV4vXn/9deHr6yuUSqXo16/fDa89NDRUzJ49u9q2W/0em4JMCCFM94ERERER0d3jPChERERkdlhQiIiIyOywoBAREZHZYUEhIiIis8OCQkRERGaHBYWIiIjMDgsKERERmR0WFCIiIjI7LChERERkdlhQiIiIyOywoBAREZHZYUEhIiIis/P/OmP6zKtxyXUAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "5IjlYM5LcxY_"}, "source": ["* There are no straight lines on the curve. \n", "* If we zoom in _infinitely_ close, however, we observe curves that _approach_ lines. \n", "* This enables us to find a slope $m$ (tangent) anywhere on the curve, including to identify where $m = 0$: "]}, {"cell_type": "code", "execution_count": 107, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "nJ1n043-cxZA", "outputId": "ca8a8659-68d2-41cd-e7c3-14770e02123d"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.set_xlim([-2, -0])\n", "ax.set_ylim([0, 2])\n", "_ = ax.plot(x,y)"]}, {"cell_type": "code", "execution_count": 108, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "tgrXcqfScxZD", "outputId": "2f9bac4e-cba5-4511-b33d-492d98c35fd8"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiMAAAGdCAYAAADAAnMpAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAMe5JREFUeJzt3Xl8VPW9//H3ZJusMyFkJ4GAYV8CoqYsWkE2a7HWVi32CuLVVn/aWvFW5QpaWhVbq2KvWlrrem9VXECtKFZRCggNa5AdQgIJWQkhmSRknTm/PxJGI0QTSPLN8no+HvNIcuacmc9wSOY93+3YLMuyBAAAYIiP6QIAAEDPRhgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYJSf6QJawuPxKC8vT2FhYbLZbKbLAQAALWBZlsrLyxUfHy8fn+bbP7pEGMnLy1NiYqLpMgAAwFnIyclRQkJCs/d3iTASFhYmqeHFOBwOw9UAAICWcLlcSkxM9L6PN6dLhJFTXTMOh4MwAgBAF/NtQywYwAoAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjGp1GFm7dq1mzpyp+Ph42Ww2vfPOOy0+9vPPP5efn59Gjx7d2qcFAADdVKvDSGVlpVJSUvTMM8+06rjS0lLNnj1bl112WWufEgAAdGN+rT3g8ssv1+WXX97qJ7r11lt1/fXXy9fXt1WtKQAAoHvrkDEjL774ojIzM/Xggw92xNMBAIAupNUtI6118OBB3XfffVq3bp38/Fr2dDU1NaqpqfH+7HK52qs8AABgWLu2jLjdbl1//fVatGiRBg0a1OLjFi9eLKfT6b0lJia2Y5UAAMAkm2VZ1lkfbLNpxYoVuuqqq854f2lpqXr16iVfX1/vNo/HI8uy5Ovrq3/+85+aPHnyacedqWUkMTFRZWVlcjgcZ1suAADoQC6XS06n81vfv9u1m8bhcGjnzp1Ntj377LP69NNP9dZbb6l///5nPM5ut8tut7dnaQAAoJNodRipqKhQRkaG9+esrCylp6crIiJCffv21fz585Wbm6tXXnlFPj4+GjFiRJPjo6OjFRgYeNp2AADQM7U6jGzZskWTJk3y/jxv3jxJ0pw5c/TSSy8pPz9f2dnZbVchAADo1s5pzEhHaWmfEwAA6Dxa+v7NtWkAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAADt4mjJyRbt16XCiKu6znQJAACgBVbtyteP/7KhRft2qTByzdINSs8pNV0GAABoRnWdW/ev2Klb/2+bKqrdLTqmS4WR3BPV+vGfN+gv/zokj8cyXQ4AAPiKA4XluvLp9fp7WrYkae6EpBYd16XCyPRhMar3WFr84T7d+NJmHSuvMV0SAAA9nmVZ+nvaEc38n/U6UFihyFC7XrnpIt09bXCLjrdZltXpmxhcLpecTqdKS0v1wf4yLfrHblXXeRQZateT16Xo4oFRpksEAKBHKjtZp/uWf6EPdxVIki4ZFKXHr0lRVJjd+/5dVlYmh8PR7GN0qTBy6sUcKCzXHa9u04HCCknSrd89T3dPGyR/3y7V0AMAQJe2+XCJ7nxtu/LKquXva9M904foPyf2l4+PTdLp79/N6ZJhRGoYIPO79/d4+6VGJ4brf2aNUWJEsMlSAQDo9tweS898lqElnxyQx5L69Q7W/8wao1EJ4U326/Zh5JQPd+brnre/UHl1vcLsfvrtVcN11eg+stlshqoFAKD7Kiir1q+Wbde/M0skSVeNjtfvrhqhsED/0/btMWFEknJKTurO17drW3apJOn7o+L08FUj5Qw+/R8GAACcnfe/yNP9K3aprKpOwQG+euiqEbr6/IRm9+9RYUSS6t0ePbvmkJ5afVBuj6U4Z6AevyZF45MjO7haAAC6F1d1nR58d7dWbM+VJI3s49SfZo1R/8iQbz6up4WRU9JzSnXXsnRlFVdKkm65uL/+a/pg2f18O6JUAAC6lX9nHtfdb+xQbmmVfGzS7ZOS9cvLBrZo0kiPDSOSdLK2Xg+t3KtXGwe3DokN05KfjNaQ2G8/FgAASDX1bj3xzwP667pMWZbUNyJYT16XorH9Ilr8GD06jJzyyZ5C3fv2FzpeWasAPx/dM32wbprw5ZQjAABwun0FLv3q9XTtKyiXJP3kwkQt+P4whdr9WvU4hJFGx8prdN/bX2j1viJJ0oTk3vrjNSmKcwa1R6kAAHRZHo+lFz7P0h9W7Vet26OIkAA9evVITRsee1aPRxj5Csuy9OqmbP3u/T2qrvPIEein31w5XD8cwxRgAAAkKa+0Sv/15g5tOHRckjR5SLR+/6NRigqzn/VjtvT9u9VLlq5du1YzZ85UfHy8bDab3nnnnW/cf/ny5Zo6daqioqLkcDg0btw4ffTRR6192nNis9n009R++uCXFyslwSlXdb3mvbFDt7yyVUXl1R1aCwAAnYllWXpjc46mP7lWGw4dV5C/rx7+4Qg9P+eCcwoirdHqMFJZWamUlBQ988wzLdp/7dq1mjp1qj744ANt3bpVkyZN0syZM7V9+/ZWF3uuBkSF6u3bxuvX0wfL39emT/YWatqTa/Vueq66QAMRAABtqqCsWnNf2tyweGhNvcb0DdfKX07UT1P7dWjPwTl109hsNq1YsUJXXXVVq44bPny4rrvuOj3wwAMt2v9cu2nOZF+BS3e/sUO781ySpBnDY/XQD0coMrRjUiAAAKZYlqW3t+Vq0T92q7y6XgF+Prp76iDdfPEA+bbhJI9266Y5Vx6PR+Xl5YqIaPnUoPYwJNahd26foLumDJKfj02rdhdo2pNrtfKLfKN1AQDQngpd1br55S36rzd3qLy6XikJTq38xUT9/LvntWkQaY3WzdFpA3/84x9VUVGha6+9ttl9ampqVFNT4/3Z5XK1Sy3+vj66c8pATRkWrbvf2KF9BeW6/dVt+nBXnH77gxGKCAlol+cFAKCjWZald9Jz9Zv39qisqk4Bvj761dSB+tnFA+Rn+Kr3Hfrsr776qhYtWqQ33nhD0dHRze63ePFiOZ1O7y0xMbFd6xoe79R7d0zULyYny9fHpve/yNe0J/+lVbsK2vV5AQDoCEXl1frZ/27VXct2qKyqTiP7OPWPX0zU/7s02XgQkTpwzMjrr7+um266SW+++aauuOKKb9z3TC0jiYmJbTpmpDlfHC3V3W/s0MGiCknS5SNitegHwxUdFtiuzwsAQFuzLEtvbjmqh1bukau6Xv6+Nv1y8kDdeul5LVrO/Vy1dMxIh3TTvPbaa7rpppv0+uuvf2sQkSS73S673cxA0lEJ4Xr/lxP11CcH9Ze1mfpwV4E+zyjWgiuG6ZoLEliXBADQJRw5Xqn5y3d61w0Z0cehx36coqFxne/SKK0OIxUVFcrIyPD+nJWVpfT0dEVERKhv376aP3++cnNz9corr0hq6JqZM2eOnnrqKaWmpqqgoKHrIygoSE6ns41eRtuy+/nqnhlDdMWoON379hfalevSPW9/oXd35GrxD0epb+9g0yUCAHBG9W6PXvg8S098fEDVdR4F+vto3tRBumlC/07RJXMmre6mWbNmjSZNmnTa9jlz5uill17SjTfeqMOHD2vNmjWSpEsvvVT/+te/mt2/Jdpjam9L1bs9en59w0mtqW84qXdPHay5E5I67UkFAPRMu/PKdN/bO7Uzt0ySNG5Abz36o5Hq1zvESD0sB9/GDhc3NHdtzGxo7hqV4NTvfzSqUzZ3AQB6luo6t/60umF4gdtjKSzQTwuuGKprL0g0OryAMNIOLMvSG1ty9NDKvSqvrpefj00//+4A/WLyQAX6+xqrCwDQc208dFz3r9ipzOJKSY0TL64crmiH+YkXhJF2VOSq1gPv7taq3Q3jX/pGBOu3PxiuSwc3P10ZAIC2VFxRo0dW7tXy7bmSpOgwu377gxGaMeLsrrDbHggjHeCj3QX6zXu7lV/WcLG9K0bGaeH3hynWaT6NAgC6J4/H0rItOXr0w30qq6qTzSZdf1Ff3TNjiJxB/qbLa4Iw0kEqa+q15JMDeuHzw3J7LIXa/XT3tEG64Tv9GOAKAGhTe/Ndun/FTm3LLpUkDY1z6OEfjtD5fXuZLawZhJEOtifPpfvf2antjf9BRvRx6OGrRiolMdxoXQCArq+ypl5PrT6o59dnye2xFBLgq3nTBmvOuM79wZcwYoDHY+n1zTl69MO9clXXy2aT/iO1n/5r+uBO13QGAOga/tk4JCCvcUjA5SNi9cDMYYpzBhmu7NsRRgz6+qCiyNAA3TNjiH58foJ8DF0REQDQtWQVV+q3/9itz/YfkyQl9ArS734wQpOGdJ3JEoSRTmDDoWItfGeXDh1rmG41OjFci64cTtcNAKBZlTX1evqzDD2/Lku1bo/8fW26+eIB+uXkgQoK6FrLSBBGOonaeo9e2pClpz45qMpat2w26dqxifr1jMGKDDVz/R0AQOdjWZbe25GnxR/sU4GroUvmu4Oi9MDMYTovKtRwdWeHMNLJFLmq9eiH+7xdN2GBfpo3lVk3AICGWTIPvrdbm7JKJDWsX7Xw+8M0ZWh0l75AK2Gkk9pyuEQPvrdbu/NckqTBMWH6zZXDNe683oYrAwB0tNKTtXri4wP6v38fkceSAv19dPulybrlkgHdYmVvwkgn5vZYen1zth77aL9KT9ZJkr43Mlb3zRjKFYEBoAeod3v02qZsPfHxAZ1ofB+4YmSc/vuKoeoT3vlnybQUYaQLKD1Zq8f/eUB/T2tIxAG+Ppo7IUm3T06WI5CpwADQ3ViWpTUHjunhlXuVUVQhSRoUE6rfzByu8cmRhqtre4SRLmRvvksPr9yr9RnFkqSIkADdNXWQZl2YyHgSAOgm9heU66GVe7Tu4Ff+1k8ZqJ9c1Ff+3fRvPWGki7EsS5/tL9LDK/d6pwIPjA7V/VcM5QJ8ANCFHSuv0ZOfHNDrm7KbtIL/v0nJ3X5BTMJIF1XX2I/45Ff6ES8ZFKX7vzdUg2PDDFcHAGip6jq3Xvg8S89+dkgVNfWSGsYH3jtjiPr1DjFcXccgjHRxZSfr9PRnB/XShsOqc1vysUnXXZioOy8bxFWBAaATc3ssvZueq8f/eUC5pVWSpJQEpxZ8f5guTIowXF3HIox0E0eOV+rRD/fpw10FkiS7n4/mTuiv2757npzB3bt5DwC6EsuytGb/Mf1+1T7tKyiXJMU5A3XvjCG6MiW+R14OhDDSzWw5XKJHP9ynLUdOSJKcQf76f5eepznjk7rFXHQA6Mq2ZZ/Qox/u8y5a5gj0022XJuvG8Uldbgn3tkQY6YYsy9LqvUX6w0f7dKCwYUpYnDNQd00ZpKvP78PMGwDoYBlF5frDqv36555CSVKAn4/mjk/SbZeep/DgAMPVmUcY6cbcHkvLtx3Vkx8f8F5SOjk6VPdMH6ypw2K69NLBANAV5JdVacnHB/Xm1hx5LMnHJv14bIJ+NWWQ4rvRomXnijDSA1TXufW/G4/omTUZ3pVcRyeGa97UQbp4YCShBADa2LHyGv15zSH9X9oR1dZ7JEnThsXo19MHa2AMMx6/jjDSg5RV1emvaw/p+fVZqq5r+OW4MKmX7po6SOPP634r+gFARyuprNVf/nVIL2887P07e1FShO69fLDG9utZM2RagzDSA50psX9nQITunja4x00nA4C2UHqyVs+ty9RLnx9WZa1bEi3QrUEY6cEKXdV65rMMvb4pR7XuhlBy8cBI3TV1kM7v28twdQDQ+bmq6/T8uiy9sD5L5Y0Llo3s49S8qYN06eAoQkgLEUag3NIqPf1pht7ckqN6T8NpnjQ4SndOGaTRieFmiwOATshVXadXNhzWX9dmylXdEEKGxIZp3tRBTBA4C4QReOWUnNSfVh/U8u25cjeGkosHRuqOSclKHdDbcHUAYF5JZa1e/DxLL204rPLGEDIwOlR3TR2kGcNje+SCZW2BMILTZBVX6ulPM/RO+peh5MKkXrpj8kBdQt8ngB6oyFWt59Zl6u9p2TrZOCZkYHSo7picrO+PipcvIeScEEbQrJySk1r6r0N6c8tR75iSUQlO3TEpWVOGxvAJAEC3l1tapb/865Be35zjHfA/oo9Dd0waqGnD+DvYVggj+FYFZac+ERzxTlUbHBOm2ycn63sjYlnRFUC3k1VcqT+vydDybbnesXRj+/XSHZOTdekgBqa2NcIIWux4RY1e+DxLL2844r3MdWJEkG6eOEDXXJCg4AA/wxUCwLnZeuSEnlubqY/2FOjUu97E5EjdPilZ3xkQQQhpJ4QRtFrZyTq9vPGwXvw8SycaV3QND/bXDd/pp9njkhQVZjdcIQC0nMdj6ZO9hfrr2kzvRUYl6bIh0bp9cjJLHXQAwgjOWlWtW29tzdHf1mfpyPGTkhou/vSj8xN088X9dV5UqOEKAaB51XVuLd+Wq7+ty1RmcaUkKcDXR1eNidctFw9g2fYORBjBOXN7LP1zd4H+sjZT6TmlkiSbTZoyNEY/v2SAxvbrRdMmgE7jRGWt/vffR/TKxsMqrqiVJIUF+uk/vtNPc8cnKdoRaLjCnocwgjZjWZY2Hz6hv649pE/2Fnm3j0pw6sbxSbpiVJzsfr4GKwTQk+0rcOnlDYe1YnuudzB+n/Ag3TSxv667MFGhdsa9mUIYQbvIKCrXc2uztCI91zsdLjI0QNen9tN/pPblkweADuFuHA/y0ueHtTHzuHf78HiHfnbJAH1vZJz8mRFoHGEE7ep4RY1e35yj/914RAWuakmSn49NV4yK043jkzSGgWEA2kHZyTot25KtlzccUW5plSTJ18emGcNjNWd8ki5Movu4MyGMoEPUuT36aHeBXvr8cJPR6ikJTt04IUmXj4hToD9dOADOTUNXzBGt2H7U2xXTK9hfsy7qq//4Tj/FhwcZrhBnQhhBh9t5tEwvbTisf+zI867s2ivYXz86P0GzUvsyCwdAq1TXufX+F/l6bVO2tn7lw87QOIfmTkjSlSnxfNjp5AgjMKa4okavpWXr1U3Zyi+r9m7/zoAIXZ/aT9OHxzDgFUCzDhaW6+9p2Vq+7aj3yrl+PjZNGx6jG8f3pyumCyGMwDi3x9Ka/UX6e1q21uwvUuPKy+odEqAfX5CgWRf2VVJkiNkiAXQK1XVufbgrX6+mZWvz4S9bQRJ6BWnWRX11zQUJig5jgHxXQxhBp5JbWqVlm7K1bEuOCl013u0Tknvr2gsSNW1YrIICaC0Bepo9eS69tfWolm8/qtLGlZ99fWy6bEi0rk/tq0sGRnHRui6MMIJOqd7t0ep9RXo1LVtrDx7zXiMizO6n76fE6cdjE3V+33CaYIFu7HhFjd5Nz9NbW49qT77Luz3eGaifXNRX112YqBiWCegWCCPo9HJKTuqtrUf19rajOnqiyrt9QGSIfjQ2QVef30dxTkbIA91Bndujz/YV6a2tR/XpviLvFXMDfH00dViMfjw2QZcMipIvrSDdCmEEXYbHYyktq0RvbT2qD3bmq6rOLUnysUkTB0bpR+f30dRhMVw9GOhiLMvS7jyXVmzP1Tvbc3W8stZ736gEp348NkFXpsQrPDjAYJVoT4QRdEkVNfX6YGe+3tp6VJuySrzbg/x9NXVYjK5Midclg6IU4MfKikBnlVVcqffS8/TujlxlHqv0bo8Mtevq8/voR+cnaHAsF6vrCQgj6PKOHK/U29ty9W56rvfqwZLkDPLX90bGamZKvFL796ZZF+gE8suq9P6OfL23I087c8u82+1+PrpsaHRDN8zAKPmxRHuPQhhBt2FZlnYcLdN76Xn6xxd5Olb+5Wyc6DC7vj8qXleOjldKgpOBr0AHKqms1Ye78vVeep42HS7xDkj39bHp4oGRujIlXlOHxSgs0N9soTCGMIJuye2xlJZ5XO/tyNMHO/O9CyJJUpwzUNOHx2rGiFhdmBRBiwnQDgrKqvXR7gKt2lWgtKzj3vWDJOmipAjNHB2v742IVe9Qu7ki0WkQRtDt1dS7tfZAsd7bkafVewt1stbtva93SICmDY/RjBFxGjegN2NMgHNw5HilVu0q0KrdBdqeXdrkvhF9HJo5Kl7fT4lXH64Pg68hjKBHqa5za93BYq3aVaBP9haqrKrOe58j0E9ThsZo2vAYTRwYpVA7s3KAb2JZlvbml+vjPYVatbtAe7+yFojNJo3t20szRsRq+vBYJUYEG6wUnR1hBD1Wndujf2ce16pdBfpod6GKK74cYxLg66PUARGaPCRak4dEq19vlqMHJKmq1q3PM4r16f4ifbavqMl1pXx9bBo3oLemj4jV9GEximZBMrQQYQRQwxiTbdkn9OHOAq3eV9hkVo4knRcVosuGxmjykGiN7ddL/oz0Rw9y9MRJfbavSJ/uK9KGQ8dVU+/x3hfo76OJyZGaPjxWU4bGqFcIa4Gg9QgjwNdYlqXM4kp9urfhj+/mwyXeVSAlKSzQTxcPjNTE5ChdPDCS5md0O1W1bm06XKL1B49p7YFi7S8sb3J/n/AgXTY0WpOGRGvcgN4K9Od6UTg3hBHgW5RV1WndwWP6dF+R1uw/ppKvrA4pSf16B2tCcqQuTo7U+PMi5QxmeiK6FrfH0q7cMq3PKNb6g8XaeuSEat1ftn742KQL+kVo0pBoXTY0WgOjQ5kejzZFGAFawe2xtONoqdYdKNb6jGPanl3apNXExyaN7OPUxIENwWRM33CWp0enc6r179+Zx7X+YLE2HDreZDC31HAxuokDIzVxYJQuGRjJUuxoV4QR4BxU1NQrLfO41h0s1vqMYmUUVTS538/HppEJTl3UP0Kp/SM0tl+EnEG0nKBjuT2W9hW4tCmrRJuySrT5cImKK5q28IXZ/TTuvN4NASQ5Uv0jQ2j9QIchjABtqKCsurGp+5jSskqazDSQGqY7Dol1KLV/hC7qH6EL+vVixgHaXHWdW7vzXN7gsflwicq/svCfJAX4+Wh0YrgmnBepiQMjlZLgZAl2GEMYAdqJZVk6eqKqyafRzOLK0/aLcwZqdGK49zYywUnXDlrMsiwdPn5S6TknlJ5dqvScUu3Jd6nO3fRPdqjdT2P79dJFjUF4VIJTdj8GnqJzIIwAHaiovFqbs05oU9ZxpWWV6EBheZNlsqWGtRoGxYRpdGK4xjSGk+ToUKYTQ5Zl6Vh5jXbnubQ9pyF47MgpPW28h9SwuvAFSb10YVKEUvv31tC4MFo+0GkRRgCDKmvqtTO3TOk5pd5PtQWu6tP2C/D10cCYUA2Lc2hYvEPD4hwaGu+QgwuLdVtuj6Ws4grtznNpT75Le/Jc2pvvOm2sh9TQ5TIi3qHRib00um9DiE3oFcSYD3QZhBGgkykoq1Z6zgltb/zUuzvPdVp//ymJEUEaFufQoJgwJUeHKjk6VOdFhbLuQxdiWZbyyqqVUVTReCvXnvxy7S9wqbrOc9r+Pjapf2SIUhLCNbpvQ9fekFgH11VCl0YYATq5U2NPTn06PvU1t7TqjPvbbFJCryANjG4MKFGhOi86VEm9gxUREsCnZUNq6t3KKanSoWMNoeNQUYUOFlXo0LGKJhdv/Kogf18NjQtrbA1zali8Q4NjwhQUQNhE99JuYWTt2rV67LHHtHXrVuXn52vFihW66qqrvvGYNWvWaN68edq9e7cSExO1YMEC3XjjjS1+TsIIepLSk7Xak+/S3vxyZRSVK6Pxza305OnjB04JtfspMSJYfSOC1K93iBIjgtUvIlh9I4IVHx7Ep+tzYFmWSiprlV1ysuF2vOHrkZKTyik5qQJXtZr7K+rnY1NSZIiSoxpat4bEhWlYnEP9eofI14fwiO6vpe/frR7aX1lZqZSUFN100026+uqrv3X/rKwsXXHFFbr11lv197//XatXr9bNN9+suLg4TZ8+vbVPD3R74cEBGn9ew+Jqp1iWpeOVtd5gcsjb9F+hAle1KmrqtTff1eTqqqf42KSoMLtiHYGKdQY2fg1SrNOuWEeQd1tP/FTu9lg6XlGj/LJqFbiqVeiqVn5ZtQrLGr82/lxVd+YWjlNCAnzVP6ohdAyMCdN5jeGjX+9gBigDLXBO3TQ2m+1bW0buvfderVy5Urt27fJu+8lPfqLS0lKtWrWqRc9DywjQvOo6t46e+PJT+6lP7EcaP8F/9eJn3yTI31cRIQHqHRqgXsEB6h0SoIiQAPUKafi+V0iAwux+Cg30U6i98RbopyB/X+NdRLX1HlXW1Kviq7fqepVW1aqksk4llTXerycq63S8skYnTtbpxMnaZls1vi7OGdikxalv78avEXSTAc1pt5aR1tq4caOmTJnSZNv06dP1q1/9qtljampqVFPz5WXfXa7TP+0BaBDo76vk6DAlR4eddp9lWTpWUaOCsuqGm6v6tO9PffKvqnMrt7Sq2TErzfGxSSF2P4XZ/RRs91OAr48C/Bpudj+fJj8H+PrIz9dH3/a+7fFYqq33qMbtUW39V25uj+oat1XVuVVRXa/ymnrVtjBwNVd/dFigYpyBinXYFecMUowjsEnLUZwzkMHDQDtq9zBSUFCgmJiYJttiYmLkcrlUVVWloKCg045ZvHixFi1a1N6lAd2ezWZTdFigosMCNSrhzPtYlqWKmnqVVNbqeGWtTjR+Lfn69ydrG1ofGgNARU29LEvyWFJ5dX2zM4M6UqC/T5NWG0egf0Nrz9daeBq22dUrxF8RwQGs0wEY1imXg5w/f77mzZvn/dnlcikxMdFgRUD3ZbPZFBbor7BAf/XrHdLi4yzLatI6UVFdr8ra+jO2YtTWe1TTuK3e/e39Ij42eVtSAvx8v9KyYmv86tsQPBq7jMLs/gq2+zI+A+ii2j2MxMbGqrCwsMm2wsJCORyOM7aKSJLdbpfdbm/v0gCcA5vNpuAAPwUH+CnadDEAurR2/xgxbtw4rV69usm2jz/+WOPGjWvvpwYAAF1Aq8NIRUWF0tPTlZ6eLqlh6m56erqys7MlNXSxzJ4927v/rbfeqszMTN1zzz3at2+fnn32Wb3xxhu666672uYVAACALq3VYWTLli0aM2aMxowZI0maN2+exowZowceeECSlJ+f7w0mktS/f3+tXLlSH3/8sVJSUvT444/rb3/7G2uMAAAASSwHDwAA2klL378Zeg4AAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwKizCiPPPPOMkpKSFBgYqNTUVG3atOkb91+yZIkGDx6soKAgJSYm6q677lJ1dfVZFQwAALqXVoeRZcuWad68eXrwwQe1bds2paSkaPr06SoqKjrj/q+++qruu+8+Pfjgg9q7d6+ef/55LVu2TP/93/99zsUDAICur9Vh5IknntAtt9yiuXPnatiwYVq6dKmCg4P1wgsvnHH/DRs2aMKECbr++uuVlJSkadOmadasWd/amgIAAHqGVoWR2tpabd26VVOmTPnyAXx8NGXKFG3cuPGMx4wfP15bt271ho/MzEx98MEH+t73vtfs89TU1MjlcjW5AQCA7smvNTsXFxfL7XYrJiamyfaYmBjt27fvjMdcf/31Ki4u1sSJE2VZlurr63Xrrbd+YzfN4sWLtWjRotaUBgAAuqh2n02zZs0aPfLII3r22We1bds2LV++XCtXrtTvfve7Zo+ZP3++ysrKvLecnJz2LhMAABjSqpaRyMhI+fr6qrCwsMn2wsJCxcbGnvGYhQsX6oYbbtDNN98sSRo5cqQqKyv1s5/9TPfff798fE7PQ3a7XXa7vTWlAQCALqpVLSMBAQEaO3asVq9e7d3m8Xi0evVqjRs37ozHnDx58rTA4evrK0myLKu19QIAgG6mVS0jkjRv3jzNmTNHF1xwgS666CItWbJElZWVmjt3riRp9uzZ6tOnjxYvXixJmjlzpp544gmNGTNGqampysjI0MKFCzVz5kxvKAEAAD1Xq8PIddddp2PHjumBBx5QQUGBRo8erVWrVnkHtWZnZzdpCVmwYIFsNpsWLFig3NxcRUVFaebMmXr44Yfb7lUAAIAuy2Z1gb4Sl8slp9OpsrIyORwO0+UAAIAWaOn7N9emAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFGEEQAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABg1FmFkWeeeUZJSUkKDAxUamqqNm3a9I37l5aW6vbbb1dcXJzsdrsGDRqkDz744KwKBgAA3Ytfaw9YtmyZ5s2bp6VLlyo1NVVLlizR9OnTtX//fkVHR5+2f21traZOnaro6Gi99dZb6tOnj44cOaLw8PC2qB8AAHRxNsuyrNYckJqaqgsvvFBPP/20JMnj8SgxMVG/+MUvdN999522/9KlS/XYY49p37598vf3P6siXS6XnE6nysrK5HA4zuoxAABAx2rp+3erumlqa2u1detWTZky5csH8PHRlClTtHHjxjMe895772ncuHG6/fbbFRMToxEjRuiRRx6R2+1u9nlqamrkcrma3AAAQPfUqjBSXFwst9utmJiYJttjYmJUUFBwxmMyMzP11ltvye1264MPPtDChQv1+OOP66GHHmr2eRYvXiyn0+m9JSYmtqZMAADQhbT7bBqPx6Po6Gj99a9/1dixY3Xdddfp/vvv19KlS5s9Zv78+SorK/PecnJy2rtMAABgSKsGsEZGRsrX11eFhYVNthcWFio2NvaMx8TFxcnf31++vr7ebUOHDlVBQYFqa2sVEBBw2jF2u112u701pQEAgC6qVS0jAQEBGjt2rFavXu3d5vF4tHr1ao0bN+6Mx0yYMEEZGRnyeDzebQcOHFBcXNwZgwgAAOhZWt1NM2/ePD333HN6+eWXtXfvXt12222qrKzU3LlzJUmzZ8/W/PnzvfvfdtttKikp0Z133qkDBw5o5cqVeuSRR3T77be33asAAABdVqvXGbnuuut07NgxPfDAAyooKNDo0aO1atUq76DW7Oxs+fh8mXESExP10Ucf6a677tKoUaPUp08f3Xnnnbr33nvb7lUAAIAuq9XrjJjAOiMAAHQ97bLOCAAAQFsjjAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjCKMAAAAowgjAADAKMIIAAAwijACAACMIowAAACjCCMAAMAowggAADCKMAIAAIwijAAAAKMIIwAAwCjCCAAAMIowAgAAjPIzXUBLWJYlSXK5XIYrAQAALXXqffvU+3hzukQYKS8vlyQlJiYargQAALRWeXm5nE5ns/fbrG+LK52Ax+NRXl6ewsLCZLPZTJdzVlwulxITE5WTkyOHw2G6nB6P89F5cC46D85F59FdzoVlWSovL1d8fLx8fJofGdIlWkZ8fHyUkJBguow24XA4uvR/rO6G89F5cC46D85F59EdzsU3tYicwgBWAABgFGEEAAAYRRjpIHa7XQ8++KDsdrvpUiDOR2fCueg8OBedR087F11iACsAAOi+aBkBAABGEUYAAIBRhBEAAGAUYQQAABhFGGlHDz/8sMaPH6/g4GCFh4e3+vhbb71VNptNS5YsafPaeprWnou6ujrde++9GjlypEJCQhQfH6/Zs2crLy+v/Yvt5s7m98KyLD3wwAOKi4tTUFCQpkyZooMHD7ZvoT1ESUmJfvrTn8rhcCg8PFz/+Z//qYqKim88pqCgQDfccINiY2MVEhKi888/X2+//XYHVdx9nc25kKSNGzdq8uTJCgkJkcPh0CWXXKKqqqoOqLjtEEbaUW1tra655hrddtttrT52xYoV+ve//634+Ph2qKznae25OHnypLZt26aFCxdq27ZtWr58ufbv368rr7yynSvt/s7m9+IPf/iD/vSnP2np0qVKS0tTSEiIpk+frurq6nastGf46U9/qt27d+vjjz/W+++/r7Vr1+pnP/vZNx4ze/Zs7d+/X++995527typq6++Wtdee622b9/eQVV3T2dzLjZu3KgZM2Zo2rRp2rRpkzZv3qw77rjjG5de75QstLsXX3zRcjqdLd7/6NGjVp8+faxdu3ZZ/fr1s5588sl2q62nae25+KpNmzZZkqwjR460bVE9VEvPhcfjsWJjY63HHnvMu620tNSy2+3Wa6+91o4Vdn979uyxJFmbN2/2bvvwww8tm81m5ebmNntcSEiI9corrzTZFhERYT333HPtVmt3d7bnIjU11VqwYEFHlNiuulh06v48Ho9uuOEG/frXv9bw4cNNl4OvKCsrk81mO6suN5y9rKwsFRQUaMqUKd5tTqdTqamp2rhxo8HKur6NGzcqPDxcF1xwgXfblClT5OPjo7S0tGaPGz9+vJYtW6aSkhJ5PB69/vrrqq6u1qWXXtoBVXdPZ3MuioqKlJaWpujoaI0fP14xMTH67ne/q/Xr13dU2W2GMNLJ/P73v5efn59++ctfmi4FX1FdXa17771Xs2bN6vIXrepqCgoKJEkxMTFNtsfExHjvw9kpKChQdHR0k21+fn6KiIj4xn/bN954Q3V1derdu7fsdrt+/vOfa8WKFUpOTm7vkrutszkXmZmZkqTf/OY3uuWWW7Rq1Sqdf/75uuyyy7rcmCrCSCvdd999stls33jbt2/fWT321q1b9dRTT+mll16SzWZr48q7n/Y8F19VV1ena6+9VpZl6c9//nMbVN79dNS5QMu09/lYuHChSktL9cknn2jLli2aN2+err32Wu3cubMNX0X30J7nwuPxSJJ+/vOfa+7cuRozZoyefPJJDR48WC+88EJbvox252e6gK7m7rvv1o033viN+wwYMOCsHnvdunUqKipS3759vdvcbrfuvvtuLVmyRIcPHz6rx+2u2vNcnHIqiBw5ckSffvoprSLNaM9zERsbK0kqLCxUXFycd3thYaFGjx59Vo/Z3bX0fMTGxqqoqKjJ9vr6epWUlHj/3b/u0KFDevrpp7Vr1y5vV3JKSorWrVunZ555RkuXLm2T19BdtOe5OPX7MGzYsCbbhw4dquzs7LMv2gDCSCtFRUUpKiqqXR77hhtuaNIvLknTp0/XDTfcoLlz57bLc3Zl7XkupC+DyMGDB/XZZ5+pd+/e7fZcXV17nov+/fsrNjZWq1ev9oYPl8ultLS0s5qp1hO09HyMGzdOpaWl2rp1q8aOHStJ+vTTT+XxeJSamnrGY06ePClJp83W8PX19X5Sx5fa81wkJSUpPj5e+/fvb7L9wIEDuvzyy8+9+I5kegRtd3bkyBFr+/bt1qJFi6zQ0FBr+/bt1vbt263y8nLvPoMHD7aWL1/e7GMwm6ZttPZc1NbWWldeeaWVkJBgpaenW/n5+d5bTU2NqZfRLZzN78Wjjz5qhYeHW++++671xRdfWD/4wQ+s/v37W1VVVSZeQrcyY8YMa8yYMVZaWpq1fv16a+DAgdasWbO89x89etQaPHiwlZaWZllWw+9GcnKydfHFF1tpaWlWRkaG9cc//tGy2WzWypUrTb2MbqG158KyLOvJJ5+0HA6H9eabb1oHDx60FixYYAUGBloZGRkmXsJZI4y0ozlz5liSTrt99tln3n0kWS+++GKzj0EYaRutPRdZWVln3P/rx6D1zub3wuPxWAsXLrRiYmIsu91uXXbZZdb+/fs7vvhu6Pjx49asWbOs0NBQy+FwWHPnzm0SDE/9Lnz1/Bw4cMC6+uqrrejoaCs4ONgaNWrUaVN90Xpncy4sy7IWL15sJSQkWMHBwda4ceOsdevWdXDl585mWZbVUa0wAAAAX8dsGgAAYBRhBAAAGEUYAQAARhFGAACAUYQRAABgFGEEAAAYRRgBAABGEUYAAIBRhBEAAGAUYQQAABhFGAEAAEYRRgAAgFH/H0D37UlwZeN+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.set_xlim([-1.5, -0.5])\n", "ax.set_ylim([0.5, 1.5])\n", "_ = ax.plot(x,y)"]}, {"cell_type": "code", "execution_count": 109, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "8osTRq2AcxZG", "outputId": "e3bf3dcf-9bfd-4db9-d6c9-e3e36b090121"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.set_xlim([-1.1, -0.9])\n", "ax.set_ylim([0.9, 1.1])\n", "_ = ax.plot(x,y)"]}, {"cell_type": "code", "execution_count": 110, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "tpcDMwpDcxZJ", "outputId": "c8ae08bf-c360-454d-901c-ff931b747cdf"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.set_xlim([-1.01, -0.99])\n", "ax.set_ylim([0.99, 1.01])\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "BJ0B4YNPcxZL"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "38-6wbQocxZL"}, "source": ["### Limits"]}, {"cell_type": "code", "execution_count": 111, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "S7GSdz-ucxZM", "outputId": "3686163d-44c8-4698-da51-be594c100f3a"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGiCAYAAAASgEe5AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAUXNJREFUeJzt3Xd4VGXexvHvTHohEwgpBBIINYQuzVAUMYIssrIgrooK4lqDgtjAFbtGsYsUURbQFQvuWrArStPQi/QaSCAkASGFhGQmM+f9IzprXkEpmZxkcn+u61zOnDlzcjNOZn55zlMshmEYiIiIiNRSVrMDiIiIiJwLFTMiIiJSq6mYERERkVpNxYyIiIjUaipmREREpFZTMSMiIiK1mooZERERqdVUzIiIiEitpmJGREREajUVMyIiIlKrebSYcTqdTJ48mYSEBIKCgmjRogWPP/44v11BwTAMHnroIRo1akRQUBApKSns2rXLk7FERETEi3i0mHnmmWeYMWMGr776Ktu2beOZZ55hypQpTJ061X3MlClTeOWVV5g5cyYrV64kJCSEgQMHUlpa6sloIiIi4iUsnlxo8rLLLiM6OprZs2e79w0fPpygoCD+/e9/YxgGsbGx3H333dxzzz0AFBQUEB0dzdy5c7nqqqs8FU1ERES8hK8nT96rVy9mzZrFzp07ad26NRs3bmT58uW88MILAGRkZJCTk0NKSor7OTabjZ49e5Kenn7SYqasrIyysjL3fZfLxdGjR4mIiMBisXjynyMiIiJVxDAMioqKiI2NxWo9twtFHi1mJk6cSGFhIYmJifj4+OB0OnnyyScZOXIkADk5OQBER0dXel50dLT7sf8vLS2NRx991JOxRUREpJpkZWXRpEmTczqHR4uZ999/n7fffpv58+fTrl07NmzYwPjx44mNjWXUqFFndc5JkyYxYcIE9/2CggLi4+PJysoiLCysqqKLnBWXy8X27dsBSExMPOe/NkSqgr3YzvOxzwNwd/bd+If4m5yobnC6DIZMXUbm0RNMGtSGkec3MztSjVJYWEhcXBz16tU753N5tJi59957mThxovtyUYcOHdi/fz9paWmMGjWKmJgYAHJzc2nUqJH7ebm5uXTu3Pmk5wwICCAgIOB3+8PCwlTMiOlcLhehoaFAxXtSxYzUBHYfO4EEAhXvSxUz1ePLzTkcKLZQP9zG9RcmERLg0a/cWqsquoh49JO2pKTkdx/mPj4+uFwuABISEoiJiWHRokXuxwsLC1m5ciXJycmejCYiIuJRry/bC8C158erkPEwj766Q4YM4cknnyQ+Pp527dqxfv16XnjhBcaMGQNUVGPjx4/niSeeoFWrViQkJDB58mRiY2MZOnSoJ6OJiNQZVl8rnUZ1ct8Wz1u7/yhr9x/D38fKqORmZsfxeh4tZqZOncrkyZO5/fbbycvLIzY2lltuuYWHHnrIfcx9991HcXExN998M/n5+fTp04cvv/ySwMBAT0YTEakzfAN8GTp3qNkx6pTXl2YAMLRLLFFh+j7zNI/OM1MdCgsLsdlsFBQUqM+MmM7lcrF161YAkpKS1GdGpA7ad6SYi55fjGHA13ddQOvoc+/g6o2q8vtbF/FERLycYRg4ShwA+AX7aU4uD5u9PAPDgIvaRKqQqSb6s1FExMs5ShykhaaRFprmLmrEM44W21mwNguAmy5obnKaukPFjIiISBV5M30fpQ4X7WLDSG4eYXacOkPFjIiISBUosZcz78d9ANx6YQtdzqtGKmZERESqwHurszhW4iC+QTCD2seYHadOUTEjIiJyjhxOF28sqxiOffMFzfH10ddrddKrLSIico4WbszmYP4JGob6c0XXc1s0Uc6cihkREZFz4HIZzFyyB4AbeicQ6OdjcqK6R/PMiIh4OauPlaQrkty3pWp9vyOPnbnHCQ3w5drzm5odp05SMSMi4uV8A30ZsWCE2TG81q+tMtf0jMcW5GdymrpJJbqIiMhZWrPvKKv3VSwoeWOfBLPj1FkqZkRERM7Sr60yf+vSmGgtKGkaXWYSEfFy9mI7aaFpAEw6Pgn/EH+TE3mHnblFfLstD4sFbr5QSxeYSS0zIiIiZ+HXVpmBSTG0iAw1OU3dpmJGRETkDB3MP8EnG7IBuLVfC5PTiIoZERGRM/TGsr2UuwySm0fQOS7c7Dh1nooZERGRM3Cs2M67q7IAuE2tMjWCihkREZEzMC99HyccTtrFhtG3VUOz4wgqZkRERE5bib2ceT/uA+DWC1tgsVjMDSSAhmaLiHg9q4+VVn9p5b4tZ++91VkcK3EQ3yCYQe1jzI4jv1AxIyLi5XwDfbnms2vMjlHr2ctdzFq6F4CbLmiOrwrDGkP/J0RERE7Dh+sPcKiglKh6AYzo2sTsOPIbKmZERET+RLnTxYzFFZPk3dS3OYF+PiYnkt/SZSYRES9nL7bzXNRzANyTd4+WMzgLn206xL6fSwgP9uOanvFmx5H/R8WMiEgd4ChxmB2h1nK5DKZ/X9EqM6Z3AiEB+uqsaXSZSURE5A98uy2XHblFhAb4Miq5mdlx5CRUzIiIiJyCYRhM+343ANclN8UW7GdyIjkZFTMiIiKnsHz3ETYeKCDQz8qNfRLMjiOnoGJGRETkFF79rqJV5qru8TQMDTA5jZyKihkREZGTWLPvKCszjuLnY+GWC5ubHUf+gEeLmWbNmmGxWH63paamAlBaWkpqaioRERGEhoYyfPhwcnNzPRlJRKTOsVgtNL2wKU0vbIrFqrWETterv/SVGX5eExrZgkxOI3/Eo+PLVq9ejdPpdN/fvHkzl1xyCSNGjADgrrvu4rPPPmPBggXYbDbGjh3LsGHD+OGHHzwZS0SkTvEL8mP04tFmx6hVNh8sYPGOw1gtFQtKSs3m0WImMjKy0v2nn36aFi1acOGFF1JQUMDs2bOZP38+/fv3B2DOnDm0bduWFStWcP7553symoiIyClNX1zRKjOkUyzNGoaYnEb+TLX1mbHb7fz73/9mzJgxWCwW1q5di8PhICUlxX1MYmIi8fHxpKenn/I8ZWVlFBYWVtpERESqyu68Ir7YnAPA7f1ampxGTke1FTMfffQR+fn5jB49GoCcnBz8/f0JDw+vdFx0dDQ5OTmnPE9aWho2m829xcXFeTC1iEjtZy+282zkszwb+Sz2YrvZcWq86Yv3YBgwICmaNjH1zI4jp6HaipnZs2czaNAgYmNjz+k8kyZNoqCgwL1lZWVVUUIREe9VcqSEkiMlZseo8bKOlvDxhmwAxvZXq0xtUS0LTOzfv59vv/2W//73v+59MTEx2O128vPzK7XO5ObmEhMTc8pzBQQEEBCgsf4iIlL1Zi7Zg9Nl0LdVQzo2CTc7jpymammZmTNnDlFRUQwePNi9r2vXrvj5+bFo0SL3vh07dpCZmUlycnJ1xBIREXE7VHCCBWsOADD2IrXK1CYeb5lxuVzMmTOHUaNG4ev7vx9ns9m48cYbmTBhAg0aNCAsLIw77riD5ORkjWQSEZFqN3PxHuxOFz0TGtCzeYTZceQMeLyY+fbbb8nMzGTMmDG/e+zFF1/EarUyfPhwysrKGDhwINOnT/d0JBERkUpyC0t5Z3VFH8xxKa1MTiNnyuPFzIABAzAM46SPBQYGMm3aNKZNm+bpGCIiIqc0Y/Ee7OUuejRrQLJaZWqdaukALCIi5rFYLcR2i3XflsryCkt5Z1UmUNEqY7HoNaptVMyIiHg5vyA/blp9k9kxaqzXlu6lrNxF16b16dVCrTK1kVbNFhGROutwURlvr9wPwLiL1SpTW6mYERGROmvW0j2UOlx0iQ+nb6uGZseRs6RiRkTEyzlKHLzU7CVeavYSjhKH2XFqjCPHy3hrhVplvIH6zIiIeDnDMCjYX+C+LRVeX7aXUoeLTk1sXNg60uw4cg7UMiMiInXO0WI7b6X/0iqjEUy1nooZERGpc15ftpcSu5OOTWxc1CbK7DhyjlTMiIhInXKs2M6bP+4D4M7+apXxBipmRESkTpm9PINiu5N2sWFc3FatMt5AxYyIiNQZ+SV25v7aKqMRTF5Do5lERLycxWIhMinSfbsu+9fyDI6XldO2URgDkqLNjiNVRMWMiIiX8wv24/Ytt5sdw3QFJQ7m/LAPgHEXt6zzhZ030WUmERGpE15ftpeisnISY+oxICnG7DhShVTMiIiI1ztabGfODxkA3HVJa6xaPdyrqJgREfFyjhIH09tNZ3q76XV2OYPXluyh2O6kfWP1lfFG6jMjIuLlDMPg8NbD7tt1zeGiMual7wNgwiWt1VfGC6llRkREvNqMxRUrY3eOC9dsv15KxYyIiHitnIJS/r2yYg2muweoVcZbqZgRERGvNe373djLXXRvVp8+LRuaHUc8RMWMiIh4pYP5J3h3dSYAEy5po1YZL6ZiRkREvNKr3+3C4TTo1SKC5BYRZscRD9JoJhERL2exWLA1tblv1wWZP5ewYM0BoGIEk3g3FTMiIl7OL9iP8fvGmx2jWr28aBflLoMLWkfSrVkDs+OIh+kyk4iIeJW9h4/z4Xq1ytQlKmZERMSrvLxoFy4DLk6MonNcuNlxpBroMpOIiJdznHAw94K5AIxeOhq/ID9zA3nQztwiPtmYDVSswSR1g4oZEREvZ7gMstdku297s5e+3YlhwMB20bRvbDM7jlQTXWYSERGvsPlgAZ9vygHUKlPXqJgRERGv8NzXOwD4a6dYEmPCTE4j1cnjxczBgwe59tpriYiIICgoiA4dOrBmzRr344Zh8NBDD9GoUSOCgoJISUlh165dno4lIiJeZFXGURbvOIyv1aIRTHWQR4uZY8eO0bt3b/z8/Pjiiy/YunUrzz//PPXr13cfM2XKFF555RVmzpzJypUrCQkJYeDAgZSWlnoymoiIeAnDMJjy5XYAruweR7OGISYnkurm0Q7AzzzzDHFxccyZM8e9LyEhwX3bMAxeeuklHnzwQS6//HIA3nzzTaKjo/noo4+46qqrPBlPRES8wPc78liz/xgBvlbu7N/K7DhiAo+2zHzyySd069aNESNGEBUVRZcuXXj99dfdj2dkZJCTk0NKSop7n81mo2fPnqSnp5/0nGVlZRQWFlbaRETkjwU3DCa4YbDZMaqcy2Xw7Fc7ARjVqxkxtkCTE4kZPFrM7N27lxkzZtCqVSu++uorbrvtNu68807mzZsHQE5ORa/z6OjoSs+Ljo52P/b/paWlYbPZ3FtcXJwn/wkiIrWef4g/9x6+l3sP34t/iL/ZcarUp5sOse1QIfUCfLntwhZmxxGTeLSYcblcnHfeeTz11FN06dKFm2++mZtuuomZM2ee9TknTZpEQUGBe8vKyqrCxCIiUls4nC5e+GUE000XNKe+lxVqcvo8Wsw0atSIpKSkSvvatm1LZmYmADExMQDk5uZWOiY3N9f92P8XEBBAWFhYpU1EROqeBWsOsO/nEiJC/BnTJ+HPnyBey6PFTO/evdmxY0elfTt37qRp06ZARWfgmJgYFi1a5H68sLCQlStXkpyc7MloIiJ1huOEg7n95jK331wcJxxmx6kSpQ4nryyqmMYj9aKWhAZoQvu6zKP/9++66y569erFU089xZVXXsmqVauYNWsWs2bNAsBisTB+/HieeOIJWrVqRUJCApMnTyY2NpahQ4d6MpqISJ1huAz2L9nvvu0N3krfT05hKY3Dgxh5frzZccRkHi1munfvzocffsikSZN47LHHSEhI4KWXXmLkyJHuY+677z6Ki4u5+eabyc/Pp0+fPnz55ZcEBqpHuoiI/F5RqYPpi3cDMC6lFQG+PiYnErN5vF3usssu47LLLjvl4xaLhccee4zHHnvM01FERMQLvL4sg2MlDlpEhjCsS2Oz40gNoLWZRESk1vj5eBmzl+0F4O4BbfD10deYqJgREZFaZPriPRTbnXRobGNQ+5OPepW6R8WMiIjUCgeOlfBWekVH5nsHtsFisZicSGoKjWUTEakD/IL9zI5wzl74eid2p4teLSLo26qh2XGkBlExIyLi5fxD/Hmg+AGzY5yTLdkFfLjhIACTBrVVq4xUostMIiJS4z39xXYMA4Z0iqVDE5vZcaSGUTEjIiI12vJdR1i26wh+PhbuHdDG7DhSA6mYERHxcuWl5cwfPJ/5g+dTXlpudpwz4nIZpH2xDYCRPZsSHxFsciKpidRnRkTEy7mcLnZ9vst9uzZZ+FM2W7ILCQ3w5Y7+Lc2OIzWUWmZERKRGKit38uxXFYsV33phcyJCA0xOJDWVihkREamR/r0ikwPHThBVL4AxfRLMjiM1mIoZERGpcQpLHbz6XcWlsbsuaU2wv3pFyKmpmBERkRpn5uI97sUkR3RtYnYcqeFUzIiISI1yqOAEs5dnADBxUFstJil/Su8QERGpUV78Zidl5S66N6tPStsos+NILaCLkCIiXs4/xJ+HjYfNjnFaduYW8cHaA0BFq4yWLZDToZYZERGpMZ7+YjsuAy5tF0PXpvXNjiO1hIoZERGpEX7YfYTvtufha7Vw76VatkBOn4oZEREvV15azoIRC1gwYkGNXc7A6TJ44rOKZQuuPb8pLSJDTU4ktYmKGRERL+dyutj6wVa2frC1xi5n8J+1B9h2qJCwQF/GXdzK7DhSy6iYERERUxWXlfPs1xXLFtx5cSvqh/ibnEhqGxUzIiJiqteW7uVwURlNI4K5Lrmp2XGkFlIxIyIipjlUcIJZS/cAMPHSRAJ8fUxOJLWRihkRETHNs1/toNRRMUHepe1jzI4jtZSKGRERMcWmAwX8d91BAB4cnKQJ8uSsqZgREZFqZxgGT3y2FYChnWPpFBdubiCp1bScgYiIl/ML9mPS8Unu2zXB11tzWZlxlABfK/demmh2HKnlVMyIiHg5i8WCfw0a7mwvd5H2ecUEeTf1bU7j8CCTE0ltp8tMIiJSrf69Yj/7fi6hYWgAt/ZrYXYc8QJqmRER8XLlZeV8esunAFz22mX4Bpj30Z9fYuflRbsAuHtAa0JNzCLew6MtM4888ggWi6XSlpj4v2ujpaWlpKamEhERQWhoKMOHDyc3N9eTkURE6hxXuYuN8zaycd5GXOXmLmfw8qJdFJxwkBhTjyu7xZmaRbyHxy8ztWvXjkOHDrm35cuXux+76667WLhwIQsWLGDJkiVkZ2czbNgwT0cSERET7Mot4s30/UDFUGwfq4ZiS9XwePuer68vMTG/nwipoKCA2bNnM3/+fPr37w/AnDlzaNu2LStWrOD888/3dDQREakmhmHw2KdbcboMBiRF06dVQ7MjiRfxeMvMrl27iI2NpXnz5owcOZLMzEwA1q5di8PhICUlxX1sYmIi8fHxpKenn/J8ZWVlFBYWVtpERKRm+3ZbHst2HcHfx8o/B7c1O454GY8WMz179mTu3Ll8+eWXzJgxg4yMDPr27UtRURE5OTn4+/sTHh5e6TnR0dHk5OSc8pxpaWnYbDb3Fhena64iIjVZWbnTPUHeP/om0DQixORE4m08eplp0KBB7tsdO3akZ8+eNG3alPfff5+goLObV2DSpElMmDDBfb+wsFAFjYhIDfav5fvY/3MJUfUCuP2ilmbHES9UrfPMhIeH07p1a3bv3k1MTAx2u538/PxKx+Tm5p60j82vAgICCAsLq7SJiEjNlFdYyqvfVQzFnjgoUUOxxSOqtZg5fvw4e/bsoVGjRnTt2hU/Pz8WLVrkfnzHjh1kZmaSnJxcnbFERLyaX7Af9+Tdwz1591T7cgbPfLmDYruTznHhDO3cuFp/ttQdHi2R77nnHoYMGULTpk3Jzs7m4YcfxsfHh6uvvhqbzcaNN97IhAkTaNCgAWFhYdxxxx0kJydrJJOISBWyWCyERFZ/P5X1mcf4z7oDADzy13ZYNRRbPMSjxcyBAwe4+uqr+fnnn4mMjKRPnz6sWLGCyMhIAF588UWsVivDhw+nrKyMgQMHMn36dE9GEhGRauByGTyysKLT7/DzmtBZq2KLB1kMwzDMDnEuCgsLsdlsFBQUqP+MmM7lcrF1a8UHeFJSElarlj8T85WXlfPVhK8AGPjCwGpZzuA/aw9w94KNhPj78P09/YgKC/T4z5TapSq/v/VJKyLi5VzlLtZMX8Oa6WuqZTmD42XlPP3ldgDG9m+lQkY8TsWMiIhUqWnf7+ZwURlNI4IZ06eZ2XGkDlAxIyIiVSbjSDGzl2UAFesvBfj6mJxI6gIVMyIiUiUMw+CRT7Zgd7q4oHUkKW2jzI4kdYSKGRERqRJfb81lyc7D+PlYeGRIEhaLhmJL9VAxIyIi5+yE3cljvwzFvvmC5jSPDDU5kdQlKmZEROScTV+8m4P5J4i1BZKq9ZekmmmRDBERL+cX5Me4jHHu21Ut40gxry3ZC8BDQ5II9tdXi1QvveNERLycxWohvFm4R85tGAaPLqzo9Nu3VUMGtjv1QsEinqLLTCIicta+3prL4h0VnX4f/Ws7dfoVU6iYERHxck67k6/v/Zqv7/0ap91ZZef9baffm/qq06+YR8WMiIiXczqcpD+XTvpz6TgdVVfM/LbT79j+6vQr5lExIyIiZ2zfbzr9Tr5MnX7FXCpmRETkjBiGwSO/6fR7aXt1+hVzqZgREZEz8o06/UoNo2JGREROW3FZOY98sgWAf6jTr9QQKmZEROS0vfTtTrILSmkcHsQd6vQrNYSKGREROS1bswv51w/7AHh8aDt1+pUaQ+9EEREv5xfkx22bb3PfPhtOl8EDH27C6TIY1D6G/onRVRlR5JyomBER8XIWq4WodlHndI75qzLZkJVPaIAvDw9pV0XJRKqGLjOJiMgfyisqZcqX2wG4e0BrYmyBJicSqUwtMyIiXs5pd7LsqWUA9H2gLz7+Pmf0/Cc+3UZRaTkdGtu4PrmZBxKKnBsVMyIiXs7pcLLk0SUA9Lq31xkVM0t3HuaTjdlYLfDU3zrgY9WcMlLz6DKTiIicVKnDyeSPNwNwfXIzOjSxmZxI5ORUzIiIyElN+343+38uITosgLsHtDY7jsgpqZgREZHf2Z1XxMwlewB4ZEg76gWe3ZBukeqgYkZERCoxDIN/frgZh9Ogf2KUFpKUGk/FjIiIVPLe6ixWZhwl0M+qhSSlVlAxIyIibnmFpTz5+TYA7hnQhrgGwSYnEvlzGpotIuLlfAN9+ceqf7hv/5GHPt5CUWk5HZvYGN2rWTWkEzl31dYy8/TTT2OxWBg/frx7X2lpKampqURERBAaGsrw4cPJzc2trkgiInWC1cdK4+6Nady9MVafU3/sf7n5EF9uycHXauHpYR3x/YNjRWqSanmnrl69mtdee42OHTtW2n/XXXexcOFCFixYwJIlS8jOzmbYsGHVEUlERH6j4ISDyR9vAeCWC5uTFBtmciKR0+fxYub48eOMHDmS119/nfr167v3FxQUMHv2bF544QX69+9P165dmTNnDj/++CMrVqzwdCwRkTrDaXfyw7M/8MOzP+C0O096TNrn2zhcVEbzyBDu6N+qmhOKnBuPFzOpqakMHjyYlJSUSvvXrl2Lw+GotD8xMZH4+HjS09NPeb6ysjIKCwsrbSIicmpOh5Nv7/uWb+/7Fqfj98XMj3uO8O7qLACeHtaRQL8zW7tJxGwe7QD87rvvsm7dOlavXv27x3JycvD39yc8PLzS/ujoaHJyck55zrS0NB599NGqjioiUieVOpw88N9NAIzsGU+PhAYmJxI5cx5rmcnKymLcuHG8/fbbBAZW3XLxkyZNoqCgwL1lZWVV2blFROqaF7/dyb6fS4gJC2TioESz44icFY8VM2vXriUvL4/zzjsPX19ffH19WbJkCa+88gq+vr5ER0djt9vJz8+v9Lzc3FxiYk4922RAQABhYWGVNhEROXObDxbwxrIMAB4f2l5LFkit5bHLTBdffDGbNm2qtO+GG24gMTGR+++/n7i4OPz8/Fi0aBHDhw8HYMeOHWRmZpKcnOypWCIiApQ7Xdz/n59wugwGd2zEJUnRZkcSOWseK2bq1atH+/btK+0LCQkhIiLCvf/GG29kwoQJNGjQgLCwMO644w6Sk5M5//zzPRVLRESAWcv2siW7EFuQH48MaWd2HJFzYuoMwC+++CJWq5Xhw4dTVlbGwIEDmT59upmRRES83s7cIl76ZhcAD12WRGS9AJMTiZwbi2EYhtkhzkVhYSE2m42CggL1nxHTuVwutm7dCkBSUhJWq2ZQFfO5nC4yl2UCENurCSNmrWDjgQL6J0Yxe1Q3LSQppqjK72+tzSQi4uWsPlaa9WsGwPTFu9l4oICwQF/ShnVQISNeQX82iojUEZUuLw1pR3RY1U2bIWImtcyIiHg5p8PJqtfW8GBePnani/6JUQw/r7HZsUSqjNcUM/ZiO3Yf++/2W32slZa8txf//phfWawW/IL8zupYR4mDU3U/slgs+AWf5bEnHBiuU3dr8g/xP6tjy0vLcTldVXKsX7Cfu6m6vKwcV3kVHRvkh8VacazT7jzpNOxnc6xvoK975eAzOtbhPOW6NgC+Ab7utk6Xw4W92H7KPjO+Ab5YfSsec5W7KC8rP+V5ffx98PllevkzOtbporz0D47188HH/8yPNVwGjhOOKjnW6muteN0AwzBwlFTRsWfwe18XPiOcdifP/XsDe/o1ISzAl0cvbXPS10+fERU8+Rnx6+/9mRzrzZ8RVcVripnnY58nkN83mbb6Syuu+ewa9/3nop475Ydg0wubMnrxaPf9l5u9TMmRkpMeG9stlptW3+S+Py1pGgX7C056bGRSJLdvud19//Xur3N46+GTHmtramP8vvHu+3MvmEv2muyTHhvcMJh7D9/rvv/2oLfZv2T/SY/1C/bjgeIH3PffH/4+uz7fddJjAR42Hnbf/vC6D9n6wdZTHjvp+CT3B9unt3zKxnkbT3nsPXn3EBIZAsBXE75izfQ1pzx2XMY4wpuFA7Don4tIf+7Ua3bdtvk2otpFAbDsqWUseXTJKY/9x6p/0Lh7xV+lK15ewbf3fXvKY0d9P8rd12DtrLV8MfaLUx579adX03JQSwAyP8vkw8kfnvLYK96/gnYjKobDbvtwGx9c+cEpj718zuV0Ht0ZgN1f7eady9455bGDXh1Ej9QeFRmWZTLvonmnPDZlSgq97+0NwKF1h3ijxxunPPbChy+k3yP9ADi87TAz2s845bHJ9yQz4NkBABRkFvBywsunPLbb7d0YPG0wACVHSngu6rlTHttpVCeGzh0KVHzZp4WmnfLYpCuSGLFghPv+Hx1bFz4jduUdZ32fWAA6/ncX/3rs94v56jPifzz5GdF6cGsANr29iY9v+PiUx9aVz4iqoj4zIiJerNzpYuLCLbh8rTTZnU/LzT+bHUmkynnN0OzD2YdPOrSrLjQhn82xakL23GWmrVu34nK4aN2ytS4z6TKT+75ZnxFvrM5kypc78C8t52+ztzB5z52Vfr9/S58RFXSZ6ZdjPfwZUZVDs72mmNE8M1ITaJ4ZqUl25hZx2SvLsTtd9Pksg1abf650yUfETFX5/a1PWhERL+RwurhnwUbsThf9WjXU5SXxaipmRES80NTvdvPTgQJsQX48PrgtmhpPvJnXjGYSEZEKG7Lymfb9bgCeGNqexg1DufrTqwHc/Y5EvIne1SIiXuSE3cmE9zbgdBn8tVMsQzpVDMn+dUiwiDfSZSYRES+S9sU29h4pJiYskMcvb292HJFqoZYZEREvsXTnYd5Mr5g489kRHbH9MoTb6XCy6e1NAHQY2cE9NFfEW6iYERHxAvkldu79oGJm3VHJTenbKtL9mNPudM82mzQiScWMeB1dZhIR8QKTP95CbmEZzSNDmDiordlxRKqVihkRkVruk43ZLNyYjY/VwotXdiaoihfxE6npVMyIiNRiOQWlPPhhRX+YsRe1pFNcuLmBREygYkZEpJZyuQzu/WAjhaXldGxiY2z/lmZHEjGFihkRkVrqXz9ksGzXEQL9rLxwZWf8fPSRLnWT3vkiIrXQ5oMFPPPldgAmX5ZEy6hQkxOJmEdDs0VEapkSeznj3l2Pw2kwICmaa3rE/+HxvgG+XPH+Fe7bIt5G72oRkVrm8U+3sedwMdFhATwzvCMWyx8vI2n1tdJuRLtqSidS/XSZSUSkFvly8yHeWZWJxQIvXNmZ+iH+ZkcSMZ1aZkREaolDBSe4/z8Vw7BvuaAFvVs2PK3nucpdbPtwGwBt/9YWq6/+jhXvone0iEgt4HQZ3PXeBgpOOOjYxMaES05/FezysnI+uPIDPrjyA8rLyj2YUsQcKmZERGqBmUv2sGLvUYL9fXj5qi74q3VFxE2/DSIiNdyGrHxe/GYnAI/8tR0JDUNMTiRSs6iYERGpwYpKHYx7dz3lLoPBHRsxomsTsyOJ1DgqZkREaijDMJj4303s/7mExuFBPDW0w58OwxapizxazMyYMYOOHTsSFhZGWFgYycnJfPHFF+7HS0tLSU1NJSIigtDQUIYPH05ubq4nI4mI1Br/XpnJZz8dwtdqYeo1XbAF+5kdSaRG8mgx06RJE55++mnWrl3LmjVr6N+/P5dffjlbtmwB4K677mLhwoUsWLCAJUuWkJ2dzbBhwzwZSUSkVtiSXcDjn24F4P5LEzkvvr7JiURqLothGEZ1/sAGDRrw7LPPcsUVVxAZGcn8+fO54oqKaba3b99O27ZtSU9P5/zzzz+t8xUWFmKz2SgoKCAsLMyT0UX+lMvlYuvWii+gpKQkrFZdyZUzd7ysnCFTl5NxpJiLE6N4Y1S3c7q85HQ42fR2xfw0HUZ2wMfPp6qiipy1qvz+rrZJ85xOJwsWLKC4uJjk5GTWrl2Lw+EgJSXFfUxiYiLx8fF/WMyUlZVRVlbmvl9YWOjx7CIi1cUwDB747yYyjhQTawvkuRGdzrmfjI+fD51Hd66agCI1kMf/bNy0aROhoaEEBARw66238uGHH5KUlEROTg7+/v6Eh4dXOj46OpqcnJxTni8tLQ2bzebe4uLiPPwvEBGpPu+uzuKTjdn4/NJPRssViPw5jxczbdq0YcOGDaxcuZLbbruNUaNGuZvhz8akSZMoKChwb1lZWVWYVkTEPNsOFfLIJxV9Cu8Z0IauTRtUyXld5S52fraTnZ/txFXuqpJzitQkHr/M5O/vT8uWLQHo2rUrq1ev5uWXX+bvf/87drud/Pz8Sq0zubm5xMTEnPJ8AQEBBAQEeDq2iEi1Ki4rJ3X+OsrKXfRrE8ktFzSvsnOXl5XzzmXvADDp+CT8fdXaI96l2nsnulwuysrK6Nq1K35+fixatMj92I4dO8jMzCQ5Obm6Y4mImMYwDB78aDN7DxcTExbI8yM6YbVqPhmR0+XRlplJkyYxaNAg4uPjKSoqYv78+SxevJivvvoKm83GjTfeyIQJE2jQoAFhYWHccccdJCcnn/ZIJhERb/D2ykw+XH8QqwVeuboLEaFqfRY5Ex4tZvLy8rj++us5dOgQNpuNjh078tVXX3HJJZcA8OKLL2K1Whk+fDhlZWUMHDiQ6dOnezKSiEiNsiErn8cWVvQjvO/SRHokVE0/GZG6xKPFzOzZs//w8cDAQKZNm8a0adM8GUNEpEY6Wmzn9n+vxe50MbBddJX2kxGpSzSjl4iICZwug3Hvrie7oJSEhiE8WwXzyYjUVSpmRERM8NK3O1m26whBfj7MvLYrYYFad0nkbFXbDMAiIlJh0bZcpn63G4C0YR1oE1PPoz/Px9+HQa8Oct8W8TYqZkREqlHmzyXc9d4GAK5PbsrQLo09/jN9/HzokdrD4z9HxCy6zCQiUk1KHU5u/fdaCkvL6RIfzoODk8yOJOIV1DIjIlINfp0Yb+uhQiJC/Jk+8jz8favn70mX00XmskwA4vvGY/XR37HiXfSOFhGpBvN+3McHaw+4J8ZrZAuqtp9dXlrOvIvmMe+ieZSXllfbzxWpLipmREQ87Mc9R3j8s20APPCXtvRu2dDkRCLeRcWMiIgHZR0tIfXtdThdBn/r0pgb+ySYHUnE66iYERHxkBJ7OTe9uYZjJQ46NrGRNqyDJsYT8QAVMyIiHmAYBvcu+IntOUU0DPVn5rVdCfTTHC8inqBiRkTEA6Yv3sNnmw7h52NhxrVdiQ2vvg6/InWNihkRkSr23fZcnvt6BwCP/rU93ZtpJWwRT9I8MyIiVWjP4eOMe2cDhgEje8ZzTc94syPh4+dDypQU920Rb6NiRkSkihwrtnPj3NUUlZXTvVl9Hh7SzuxIQMV6TL3v7W12DBGP0WUmEZEqYC93cdvba9n3cwmNw4OYPrJrtc3wK1LXqWVGROQcGYbB5I82s2LvUUIDfPnX6O5E1gswO5aby+ni0LpDADQ6r5GWMxCvo3e0iMg5emNZBu+tycJqgalXd6FNTD2zI1VSXlrOGz3e4I0eb2g5A/FKKmZERM7BN1tzeeqLiqUKJl+WxEWJUSYnEql7VMyIiJylLdkFjHt3PYYB154fz+hezcyOJFInqZgRETkLeYWl/GPeGkrsTvq0bMjDQ9ppqQIRk6iYERE5QyfsTm56cw2HCkppERnCtJHn4adOtSKm0W+fiMgZcLoMxr27no0HCqgf7Me/RnfHFuRndiyROk3FjIjIaTIMg0cXbuHrrbn4+1p57bpuNI0IMTuWSJ2neWZERE7TrKV7eTN9PxYLvHhlZ3ok1I41l3z8fLjw4Qvdt0W8jYoZEZHT8MnGbNK+2A7AP//SlsEdG5mc6PT5+PvQ75F+ZscQ8RhdZhIR+RPpe37mnvc3AnBD72b8o29zkxOJyG+pZUZE5A/szC3i5rfWYHe6GNQ+hgcHJ5kd6YwZLoPD2w4DENk2EotVQ8jFu6hlRkTkFHILSxn9r1UUlZbTrWl9Xvx7Z3xqYSHgOOFgRvsZzGg/A8cJh9lxRKqcihkRkZMoLHUwes5qsgtKaR4ZwuvXdyNQnWdFaiSPFjNpaWl0796devXqERUVxdChQ9mxY0elY0pLS0lNTSUiIoLQ0FCGDx9Obm6uJ2OJiPyhUoeTf8xbw7ZDhTQM9WfeDT2oH+JvdiwROQWPFjNLliwhNTWVFStW8M033+BwOBgwYADFxcXuY+666y4WLlzIggULWLJkCdnZ2QwbNsyTsURETqnc6WLs/HWsyjhKvQBf5t7Qg7gGwWbHEpE/4NEOwF9++WWl+3PnziUqKoq1a9dywQUXUFBQwOzZs5k/fz79+/cHYM6cObRt25YVK1Zw/vnnezKeiEglLpfB/f/ZxLfb8gjwtfLGqG60b2wzO5aI/Ilq7TNTUFAAQIMGFRNNrV27FofDQUpKivuYxMRE4uPjSU9PP+k5ysrKKCwsrLSJiJwrwzB48vNt/GfdAXysFqZdcx49m0eYHUtETkO1FTMul4vx48fTu3dv2rdvD0BOTg7+/v6Eh4dXOjY6OpqcnJyTnictLQ2bzebe4uLiPB1dROqA6Yv3MHt5BgBThnckJSna5EQicrqqbZ6Z1NRUNm/ezPLly8/pPJMmTWLChAnu+4WFhSpoROSczF+ZybNfVQxOmHxZEsO7NjE5UdXy8fMh+Z5k920Rb1MtxczYsWP59NNPWbp0KU2a/O9DIiYmBrvdTn5+fqXWmdzcXGJiYk56roCAAAICAjwdWUTqiM9+OsQ/P9oEwNiLWnJjnwSTE1U9H38fBjw7wOwYIh7j0ctMhmEwduxYPvzwQ7777jsSEip/SHTt2hU/Pz8WLVrk3rdjxw4yMzNJTk72ZDQREb7Zmsu4d9djGHBNz3juHtDa7EgichY82jKTmprK/Pnz+fjjj6lXr567H4zNZiMoKAibzcaNN97IhAkTaNCgAWFhYdxxxx0kJydrJJOIeNSSnYdJfXsd5S6DyzvH8vjl7bFYat/svqfDcBkUZFYMwLDF27ScgXgdjxYzM2bMAKBfv36V9s+ZM4fRo0cD8OKLL2K1Whk+fDhlZWUMHDiQ6dOnezKWiNRx6Xt+5uY3/7fe0vMjOtXKZQpOl+OEg5cTXgZg0vFJ+GsCQPEyHi1mDMP402MCAwOZNm0a06ZN82QUEREA1u4/yo3zVlNW7uLixChevqoLvj5a2UWkNtNvsIjUGT8dyGf0v1ZTYnfSt1VDpo08D39ffQyK1Hb6LRaROmFrdiHXzV5FUVk5PRIaMOs6LRwp4i1UzIiI19uRU8R1s1dScMJBl/hw/jW6O0H+KmREvEW1TZonImKGrdmFXDt7JUeL7bRvHMbcG3oQGqCPPhFv4jW/0afT2VhE6pYt2QWMfGMl+SUOOjax8daYntiC/MyOJSJVzGsuM6V9vg2XSwWNiFTYfLCAa16vKGQ6xYXz1o09sQXXzULG6mul2+3d6HZ7N6zq8CxeyGtaZuavysISEMyTQztg9eL5IkTkz/10IJ9r31hJYWk5XeLDmTemB2GBdbOQAfAN8GXwtMFmxxDxGK8p0a0WeGdVFvd8sBGnWmhE6qwNWfmM/KWQ6dq0Pm/W8UJGpC7wmmImbVgHfKwW/rvuIOPf24DD6TI7kohUs7X7j3HdGyspKi2ne7P6zBvTg3oqZDAMg+LDxRQfLlb/QvFKXlPMDO4Yy7RruuDnY2HhxmzGzl+HvVwFjUhdsXzXEa6bvdI9j4xGLf2Po8TBc1HP8VzUczhKHGbHEalyXlPMAFzavhEzr+2Kv4+Vr7bkcuu/11LqcJodS0Q87KstOYyZ+7+Zfefe0J0QFTIidYZXFTMAF7eN5o1R3QjwtfLd9jxGz1lFUan+EhHxVv9dd4Db317nXjTyjVHdCPZXISNSl3hdMQNwQetI5o2paGJesfcoV7++giPHy8yOJSJV7M30fUx4v6LT/xVdmzD16i4E+GpmX5G6xiuLGYDzm0fw7s3nExHiz+aDhYyYmc6BYyVmxxKRKmAYBtO+381DH28BYHSvZkwZ3lGrX4vUUV79m9++sY0FtybTODyIjCPFXDEjnV25RWbHEpFz4HIZpH2xnWe/2gHAnRe34uEhSZpfSqQO8+piBqB5ZCgf3JZMq6hQcgpLGfFaOuszj5kdS0TOgr3cxYT3NzBr6V4AHhzclgmXtMZiUSEjUpd5fTED0MgWxPu3JNM5Lpz8Egcj31jJ0p2HzY4lImegqNTBmLmr+WhDNr5WC8+N6MQ/+jY3O1atYPW10mlUJzqN6qTlDMQrWYxaPoNSYWEhNpuNgoICwsLC/vDY4rJybv33WpbtOoKv1cJTwzpwZbe4akoqdYHL5WLr1q0AJCUlYbXqi6Mq5BWWMnrOarYeKiTY34fpI8+jX5sos2OJyDk4k+/vP1OnPmlDAnyZPao7QzvHUu4yuO+Dn3jxm52aEVOkBttz+DjDZvzI1kOFNAz1572bk1XIiEgldaqYAfD3tfLi3zsz9qKWALy8aBf3LPhJswWL1EBr9x9j+IwfOXDsBM0igvnvbb3p0MRmdqxaxzAM7MV27MV2/fEmXqnOFTMAFouFewa24am/Vazn9J91B7hh7ioKNbmeSI3x+aZDXPP6CvJLHHSKC+c/t/UiPiLY7Fi1kqPEQVpoGmmhaVrOQLxSnSxmfnVNz/hfZgv14YfdPzNiRjrZ+SfMjiVSp/06h8ztb6+jrNxF/8Qo3rmpJxGhAWZHE5Eaqk4XMwAXtYni/VuSiawXwI7cIoZO+4GNWflmxxKpk8rKndz9/kb3HDJjeifw+vVankBE/lidL2agYnK9j1J70zo6lLyiMq58LZ1PNmabHUukTjlabOfaN1by3/UH8bFaeGJoex4akoSPJsMTkT+hYuYXjcOD+M9tveifGEVZuYs731nP81/vwOVSZzkRT9udV9EqunrfMeoF+jL3hu5ce35Ts2OJSC2hYuY36gX68fr13bjlgoqJuKZ+t5vb3l5LcVm5yclEvNf3O/L42/QfyTxaQnyDYD68vRd9W0WaHUtEahEVM/+Pj9XCpL+05fkRnfD3sfLVltxfhoZqkUqRqvRrR98xc1dTVFpO92b1+Si1Ny2j6pkdTURqGfWqO4XhXZvQrGEIt7y1lu05RVz+6g+8es15JLeIMDuaSK13vKycexds5IvNOUDFyMJHhrTDX1Pte4TVx0rSFUnu2yLepk4tZ3A2svNPcNOba9iSXYiP1cL9l7bhpr7NtbCdnJSWM/hzGUeKufnNNezKO46fj4XHLm/P1T3izY4lItVMyxlUo9jwID64tRfDujTG6TJ46vPt3P72Oo6rH43IGft+ex5/fXU5u/KOE1UvgHdvTlYhIyLnzKPFzNKlSxkyZAixsbFYLBY++uijSo8bhsFDDz1Eo0aNCAoKIiUlhV27dnky0lkJ8vfh+Ss78fjQ9vj5WPhicw6Xv7qc3XlFZkcTqRWcLoMXv9nJmHkV/WO6Nq3Pp3f0oWvT+mZHExEv4NFipri4mE6dOjFt2rSTPj5lyhReeeUVZs6cycqVKwkJCWHgwIGUlpZ6MtZZsVgsXHd+U967JZmYsED2HC7mr6/+wKc/aT4akT+SV1TKdbNX8vKiXRhGRf+Yd246n6iwQLOj1Rn2YjuPWh7lUcuj2IvtZscRqXIe7QA8aNAgBg0adNLHDMPgpZde4sEHH+Tyyy8H4M033yQ6OpqPPvqIq666ypPRztp58fX59M4+3PnOen7c8zNj569ndcZRJv2lLYF+PmbHE6lRftxzhHHvbuBwURlBfj48+bf2DDuvidmxRMTLmNZnJiMjg5ycHFJSUtz7bDYbPXv2JD09/ZTPKysro7CwsNJW3RqGBvDmmB7cemELAOal72fY9B/Zc/h4tWcRqYmcLoNXFu3i2jdWcriojNbRoSy8o7cKGRHxCNOKmZyciiGZ0dHRlfZHR0e7HzuZtLQ0bDabe4uLi/NozlPx9bEycVAic27oToMQf7YeKmTI1OX8Z+0BU/KI1BSHi8oYPWcVL3yzE5cBI7o24ePUPpo/RkQ8ptaNZpo0aRIFBQXuLSsry9Q8F7WJ4otxfenVIoISu5O7F2xkwnsbNNpJ6qTvtucy6OWlLNt1hEA/K8+N6MSzIzoR5K9LsCLiOaYVMzExMQDk5uZW2p+bm+t+7GQCAgIICwurtJktOiyQt27syT0DWmO1wH/XH2TI1OX8dCDf7Ggi1eKE3cnkjzYzZu4ajhy30ya6Hp+M7cMVXXVZSUQ8z7RiJiEhgZiYGBYtWuTeV1hYyMqVK0lOTjYr1lnzsVoY278V792STKwtkIwjxQyb/iMvf7sLh9NldjwRj9l8sIAhry7nrRX7ARjTO4GPx/amdbQuK4lI9fDoaKbjx4+ze/du9/2MjAw2bNhAgwYNiI+PZ/z48TzxxBO0atWKhIQEJk+eTGxsLEOHDvVkLI/q3qwBn4/ryz8/2sxnPx3ixW938t32XF74e2daRIaaHU+kyrhcBrOW7eX5r3fgcBpE1gvg+RGduKC1Fomsaaw+Vlr9pZX7toi38ehyBosXL+aiiy763f5Ro0Yxd+5cDMPg4YcfZtasWeTn59OnTx+mT59O69atT/tneHo5g3Px8YaDTP5oM4Wl5QT4Wpk0KJHrk5thtWopBG9VV5Yz2HekmPs++IlV+44CMCApmqeHd6RBiL/JyUSktqjK72+tzeRhOQWl3PvBRpbtOgJA75YRTLmiE43Dg0xOJp7g7cWM02Uw54cMnvt6B6UOF8H+Pjx0WRJ/7x6n9cpE5IxobaZaJMYWyJtjevDY5e0I9LPyw+6fGfDCEub9uA+Xq1bXkVLH7Dl8nCtfS+eJz7ZR6nDRq0UEX42/gKt6xKuQERFTqZipBhaLheuTm/H5nX3p1rQ+xXYnD3+yhStm/siuXK3vJDWb02Xw+tK9/OXlZazdf4zQAF+e+lsH3v5HT+IaBJsdT06DvdjOUyFP8VTIU1rOQLySiplq1DwylPdvSebxy9sR4u/Dusx8Br+ynJe/3YW9XCOepObZdKCAodN+4MnPt1FW7qJvq4Z8ddcFXNNTrTG1jaPEgaPEYXYMEY/w6Ggm+T2r1cJ1yc24uG00D360me+25/Hitzv5fNMhnvxbe7o1a2B2RBEKSx288PVO3kzfh8uAeoG+/PMvbdU3RkRqJBUzJokND2L2qG58sjGbRxduZUduEVfMTGf4eU2YOCiRyHoBZkeUOsgwDD7bdIjHFm4lr6gMgMs7x/LPwW2JqqdVrkWkZlIxYyKLxcLlnRvTt1UkU77czrurs/jPugN8vTWHuy9pzbXnN8VXc0JINdl7+DiPLtzKkp2HAUhoGMLjl7enT6uGJicTEfljKmZqgAYh/jw9vCNXdo/joY83s/lgIY8s3Mp7aw7w+OXtdOlJPKrghIOpi3YxL30fDqeBv4+V2/q14LZ+LQj005pKIlLzqZipQc6Lr8/HqX14Z1Umz361g22HCrliZjpDOsVy38A2GjkiVcrpMnh3dSbPf72To7+McLmoTSSTL0uiuWarFpFaRMVMDeNjtXDt+U35S4dGTPlyO++tyWLhxmy+2pzD6N7NSO3XEluwn9kxpZb7cc8RHlu4le05FVMDtIgMYfJlSfRrE2VyMvEEi9VC0wubum+LeBvNAFzDbcku4KnPt/HD7p8BCA/2487+rbj2/Kb4+6o/TU1T02cA3pJdwLNf7WDxjop+MbYgP8anVLyf/NQ/S0SqkZYz+A1vL2agYoTJ4p2HeeqzbezKOw5A04hgxqe04q+dGuOjv7RqjJpazOw7UswL3+zkk43ZAPhaLVzTM567UlpTX+spiYgJVMz8Rl0oZn5V7nTx/poDvPDNTo4crxg22zIqlLtSWjOofYwWsKwBaloxk1dYyivf7eLdVVmU/7J8xl87xTLhktY0axhiajYRqduq8vtbfWZqEV8fK9f0jOfyzrHM/XEfs5buZXfecVLnr6NtozDuSmnFJUnRmtRMOFRwgteW7OWdVZmU/TK79EVtIrlnYBvaxdpMTifVzV5s5+VmLwMwbt84/NUaJ15GxUwtFBLgS+pFLbkuuSmzl2Xwr+UZbDtUyM1vraV94zBu79eSge1idPmpDso6WsL0xXv4YG0WDmdFS0zXpvW5b2AbejaPMDmdmKnkSInZEUQ8RsVMLRYW6Mddl7Tmht7NmLV0L3N/3Mfmg4Xc/vY6EhqGcMsFzfnbeY0J8NVcId5ud95xZi7Zw4frD+L85XLS+c0bcGf/ViS3iFBrnYh4NRUzXiA82J/7Lk3kH32bM/fHfcz7cR8ZR4qZ+N9NvPDNTm7sk8A1PeOpF6gh3d7EMAzS9/zMG8sz+G57nnt/31YNufPiVnTXZIsiUkeomPEiDUL8mXBJa265oDnvrMrkjWUZ5BSWkvbFdl5ZtIvhXZtwfXIzWkZpQrTazF7uYuHGbN745fIigMUCKW2jSb2oJZ3jws0NKCJSzVTMeKGQAF/+0bc51yc346MNB90dhd9M38+b6fvp26oho5KbcVFilPrV1CIHjpXw/uos3l2d5V4EMsjPhyu7NeGG3gkanSQidZaKGS/m72vlym5xjOjahB92/8zcH/exaHsuy3YdYdmuI8Q1COKq7vEMP68JMTatiFwTlTtdfL/jMPNX7mfxzsP8OpFCdFgAo3slcHWPOMKDNTJFROo2FTN1gMVioU+rhvRp1ZCsoyW8tWI/763OIuvoCZ79agfPf72DC1pHcmW3OC5uG6UOwzXA3sPH+Wj9Qd5fc4CcwlL3/l4tIrimZzwDkmI0A7ScNovVQmy3WPdtEW+jSfPqqBN2J5/+lM2CtQdYlXHUvb9+sB+Xd27MkE6N6BJXXxPxnaFzmTTv5+NlfPrTIf67/iAbs/Ld+xuE+HNF1yZc1T1OC0CKiNfQDMC/oWLm3GUcKeaDtVl8sPYAuYVl7v2xtkAu6xTLZR0b0aGxTcN7T8OZFjMFJQ6+3ZbLZ5sOsWTnYfewah+rhb6tGvK3Lo25tH2MWstExOuomPkNFTNVx+kyWLrrMJ9syOabrbkcLyt3P9Y0IpiB7WK4ODGKrk3r46tFCU/qdIqZvMJSvtqay1ebc1ix92f3MgMAHZvY+FuXxlzWMZbIegHVlltEpLqpmPkNFTOeUepwsnhHHgt/OsSibbmUOlzux2xBfvRrE0n/xCj6tY7CFqz5a351smKm3Oli44EClu48zJKdh9nwm0tIAIkx9RjQLoa/dorVsHnxCEeJg2lJ0wBI3ZqKn35npQbQ2kzicYF+PlzavhGXtm9Eib2c77bn8d22PL7fkcexEgcfb8jm4w3ZWC3QobGN5BYNSW4RQfdm9Qn2r9tvK8MwyDlezk+rs1i++wjLdx2hsLS80jFd4sO5tF0MA9vFaEi1eJxhGBTsL3DfFvE2dftbR05LsL8vl3WM5bKOsThdBuszj/HttjwWbctlV95xNh4oYOOBAmYu2YOfj4XOceF0a9aALnHhdI4PJ6qedw/7Lne62J5TxKqMo6zed5QVuw9zrNQJZLuPsQX50adlQ/q2aki/NlEaCi8iUoVUzMgZ8bFa6NasAd2aNWDioERyCkpJ33uEH3f/zI97fuZg/glW7zvG6n3H3M9pHB5E57hwOsXZSIwJIzGmHpH1Amplh2J7uYuduUVszS5kc3YBW7IL2ZpdyAmHs9Jxvlbo1CScvq0juaB1JJ2ahGuCQhERD1ExI+ckxhbI37o04W9dmmAYBllHT5C+9wjrM/NZn5nPzrwiDuaf4GD+CT7bdMj9vPBgP9pE16NNTD2aNwwhPiKY+AbBNKkfTKCfuSN3DMMgv8RBxs/F7D1czN7Dxyv+e+Q4GUeK3atR/1a9AF+6NqtP16b1iaSAVg386dKx/RkNzRYRkbOjYkaqjMViqShKIuL5e/d4AI6XlfPTgXw2ZOWz+WABO3KKyDhSTH6Jg5UZR1n5mzlufhUTFkjj+kFE1Qsgsl4ADUP/99+wQF9CAnwJDfAlNLDivwG+1lO28rhcBnanizKHi7JyJ8fLysk/4aCgxEH+CTvHih0cLbZzqKCUnMITHMov5VBB6e9aWn4rLNCXdrE22jcOo12sjXaxYTSPDMXHaqnUAVhERKqHihnxqNAAX3q1aEivFg3d+0odTnbnHWdnbhE7corY/3MJmUcrtuNl5eQUllaa9fZ0WC1gtViwWi34WCxYLOBwuk7ainK6osMCaN4wlOaRITSPrPhvy8hQmtQPqpWXyEREvJWKGal2gX4+tG9so31jW6X9hmFwrMRB5tESDh47wZHjZRw5XsbhoortyPEyikrLOV5WTnFZOcX2/7WeuAxwGUbFjVOwWCDU35fwED/Cg/wJD/bDFuRH/WB/YmyBNLIF0sgWRCNbIDG2QNMvd4lUFYvFQmRSpPu2iLepEcXMtGnTePbZZ8nJyaFTp05MnTqVHj16mB1LqpnFYqFBiD8NQvzpHBf+p8e7XAYlDielDicul4HTMCqKGpeByzDw97Xi72MlwM+HAF8rvlaLPsilTvIL9uP2LbebHUPEY0wvZt577z0mTJjAzJkz6dmzJy+99BIDBw5kx44dREVFnfZ5XC4XLpfrzw8UrxLsZyXY7/Q62RqG4fE5Nn77HtT7UUTk1KryM9L0GYB79uxJ9+7defXVV4GKf1xcXBx33HEHEydO/N3xZWVllJX9b/2gwsJC4uLiSE9PJzRUs6eKiIjUBsePHyc5OblKZgA2ddyo3W5n7dq1pKSkuPdZrVZSUlJIT08/6XPS0tKw2WzuLS4urrriiojUSuUnyvl66Nd8PfRryk+U//kTRGoZUy8zHTlyBKfTSXR0dKX90dHRbN++/aTPmTRpEhMmTHDf/7VlJjExUWszielcLpf7vZuYmKh5ZqRGsBfb+XjPx0DF+9I/xN/kRCIV399VxfQ+M2cqICCAgIDfryZstVr1xSE1it6TUlP89n2o96XUFFX5PjT1Hd2wYUN8fHzIzc2ttD83N5eYmBiTUomIiEhtYmox4+/vT9euXVm0aJF7n8vlYtGiRSQnJ5uYTERERGoL0y8zTZgwgVGjRtGtWzd69OjBSy+9RHFxMTfccIPZ0URERKQWML2Y+fvf/87hw4d56KGHyMnJoXPnznz55Ze/6xQsIiIicjKmFzMAY8eOZezYsWbHEBHxShaLBVtTm/u2iLepEcWMiIh4jl+wH+P3jTc7hojHaHyeiIiI1GoqZkRERKRW02UmEREv5zjhYO4FcwEYvXQ0fkF+5gYSqWIqZkREvJzhMshek+2+LeJtdJlJREREajUVMyIiIlKrqZgRERGRWk3FjIiIiNRqKmZERESkVtNoJhGROiC4YbDZEUQ8RsWMiIiX8w/x597D95odQ8RjdJlJREREajUVMyIiIlKr6TKTiIiXc5xw8PagtwEY+cVILWcgXkfFjIiIlzNcBvuX7HffFvE2uswkIiIitZqKGREREanVVMyIiIhIraZiRkRERGo1FTMiIiJSq2k0k4hIHeAXrOHY4r1UzIiIeDn/EH8eKH7A7BgiHqPLTCIiIlKrqZgRERGRWk3FjIiIlysvLWf+4PnMHzyf8tJys+OIVDn1mRER8XIup4tdn+9y3xbxNmqZERERkVpNxYyIiIjUaipmREREpFbzWDHz5JNP0qtXL4KDgwkPDz/pMZmZmQwePJjg4GCioqK49957KS9X5zQRERE5fR7rAGy32xkxYgTJycnMnj37d487nU4GDx5MTEwMP/74I4cOHeL666/Hz8+Pp556ylOxRERExMt4rJh59NFHAZg7d+5JH//666/ZunUr3377LdHR0XTu3JnHH3+c+++/n0ceeQR/f/+TPq+srIyysjL3/YKCAgAKCwur9h8gchZcLhfHjx8HKt6TVquu5Ir57MV2SikFKt6X/s6Tf76KVKdfv7cNwzj3kxkeNmfOHMNms/1u/+TJk41OnTpV2rd3714DMNatW3fK8z388MMGoE2bNm3atGnzgm3Pnj3nXGuYNs9MTk4O0dHRlfb9ej8nJ+eUz5s0aRITJkxw33e5XBw9epSIiAgsFotnwv6JwsJC4uLiyMrKIiwszJQMNYFehwp6HSrodfgfvRYV9DpU0OtQoaCggPj4eBo0aHDO5zqjYmbixIk888wzf3jMtm3bSExMPKdQfyQgIICAgIBK+07Vwbi6hYWF1ek35q/0OlTQ61BBr8P/6LWooNehgl6HClVxOf6Mipm7776b0aNH/+ExzZs3P61zxcTEsGrVqkr7cnNz3Y+JiIiInI4zKmYiIyOJjIyskh+cnJzMk08+SV5eHlFRUQB88803hIWFkZSUVCU/Q0RERLyfx/rMZGZmcvToUTIzM3E6nWzYsAGAli1bEhoayoABA0hKSuK6665jypQp5OTk8OCDD5Kamvq7y0g1XUBAAA8//HCty13V9DpU0OtQQa/D/+i1qKDXoYJehwpV+TpYDKMqxkT93ujRo5k3b97v9n///ff069cPgP3793PbbbexePFiQkJCGDVqFE8//TS+vlr/UkRERE6Px4oZERERkeqgGb1ERESkVlMxIyIiIrWaihkRERGp1VTMiIiISK2mYsZDysrK6Ny5MxaLxT0sva7Yt28fN954IwkJCQQFBdGiRQsefvhh7Ha72dGqxbRp02jWrBmBgYH07Nnzd5NDeru0tDS6d+9OvXr1iIqKYujQoezYscPsWKZ7+umnsVgsjB8/3uwo1e7gwYNce+21REREEBQURIcOHVizZo3ZsaqV0+lk8uTJlT4XH3/88apZZLGGW7p0KUOGDCE2NhaLxcJHH31U6XHDMHjooYdo1KgRQUFBpKSksGvXrjP6GSpmPOS+++4jNjbW7Bim2L59Oy6Xi9dee40tW7bw4osvMnPmTB544AGzo3nce++9x4QJE3j44YdZt24dnTp1YuDAgeTl5ZkdrdosWbKE1NRUVqxYwTfffIPD4WDAgAEUFxebHc00q1ev5rXXXqNjx45mR6l2x44do3fv3vj5+fHFF1+wdetWnn/+eerXr292tGr1zDPPMGPGDF599VW2bdvGM888w5QpU5g6darZ0TyuuLiYTp06MW3atJM+PmXKFF555RVmzpzJypUrCQkJYeDAgZSWlp7+DznnpSrldz7//HMjMTHR2LJliwEY69evNzuS6aZMmWIkJCSYHcPjevToYaSmprrvO51OIzY21khLSzMxlbny8vIMwFiyZInZUUxRVFRktGrVyvjmm2+MCy+80Bg3bpzZkarV/fffb/Tp08fsGKYbPHiwMWbMmEr7hg0bZowcOdKkROYAjA8//NB93+VyGTExMcazzz7r3pefn28EBAQY77zzzmmfVy0zVSw3N5ebbrqJt956i+DgYLPj1BgFBQVVsjJqTWa321m7di0pKSnufVarlZSUFNLT001MZq6CggIAr///fyqpqakMHjy40vuiLvnkk0/o1q0bI0aMICoqii5duvD666+bHava9erVi0WLFrFz504ANm7cyPLlyxk0aJDJycyVkZFBTk5Opd8Pm81Gz549z+hzU1PtViHDMBg9ejS33nor3bp1Y9++fWZHqhF2797N1KlTee6558yO4lFHjhzB6XQSHR1daX90dDTbt283KZW5XC4X48ePp3fv3rRv397sONXu3XffZd26daxevdrsKKbZu3cvM2bMYMKECTzwwAOsXr2aO++8E39/f0aNGmV2vGozceJECgsLSUxMxMfHB6fTyZNPPsnIkSPNjmaqnJwcgJN+bv762OlQy8xpmDhxIhaL5Q+37du3M3XqVIqKipg0aZLZkT3idF+H3zp48CCXXnopI0aM4KabbjIpuZglNTWVzZs38+6775odpdplZWUxbtw43n77bQIDA82OYxqXy8V5553HU089RZcuXbj55pu56aabmDlzptnRqtX777/P22+/zfz581m3bh3z5s3jueeeO+myP3Lm1DJzGu6++25Gjx79h8c0b96c7777jvT09N8tmtWtWzdGjhxZ69+0p/s6/Co7O5uLLrqIXr16MWvWLA+nM1/Dhg3x8fEhNze30v7c3FxiYmJMSmWesWPH8umnn7J06VKaNGlidpxqt3btWvLy8jjvvPPc+5xOJ0uXLuXVV1+lrKwMHx8fExNWj0aNGpGUlFRpX9u2bfnPf/5jUiJz3HvvvUycOJGrrroKgA4dOrB//37S0tLqVAvV//frZ2Nubi6NGjVy78/NzaVz586nfR4VM6chMjKSyMjIPz3ulVde4YknnnDfz87OZuDAgbz33nv07NnTkxGrxem+DlDRInPRRRfRtWtX5syZg9Xq/Y2A/v7+dO3alUWLFjF06FCg4q/SRYsWMXbsWHPDVSPDMLjjjjv48MMPWbx4MQkJCWZHMsXFF1/Mpk2bKu274YYbSExM5P77768ThQxA7969fzc0f+fOnTRt2tSkROYoKSn53eegj48PLpfLpEQ1Q0JCAjExMSxatMhdvBQWFrJy5Upuu+220z6PipkqFB8fX+l+aGgoAC1atKhTf5kePHiQfv360bRpU5577jkOHz7sfszbWygmTJjAqFGj6NatGz169OCll16iuLiYG264wexo1SY1NZX58+fz8ccfU69ePfd1b5vNRlBQkMnpqk+9evV+108oJCSEiIiIOtV/6K677qJXr1489dRTXHnllaxatYpZs2bVidba3xoyZAhPPvkk8fHxtGvXjvXr1/PCCy8wZswYs6N53PHjx9m9e7f7fkZGBhs2bKBBgwbEx8czfvx4nnjiCVq1akVCQgKTJ08mNjbW/Ufhaam6AVfy/2VkZNTJodlz5swxgJNudcHUqVON+Ph4w9/f3+jRo4exYsUKsyNVq1P9v58zZ47Z0UxXF4dmG4ZhLFy40Gjfvr0REBBgJCYmGrNmzTI7UrUrLCw0xo0bZ8THxxuBgYFG8+bNjX/+859GWVmZ2dE87vvvvz/pZ8KoUaMMw6gYnj158mQjOjraCAgIMC6++GJjx44dZ/QzLIZRB6YfFBEREa/l/R0ZRERExKupmBEREZFaTcWMiIiI1GoqZkRERKRWUzEjIiIitZqKGREREanVVMyIiIhIraZiRkRERGo1FTMiIiJSq6mYERERkVpNxYyIiIjUav8HqIdi/Y4GKcUAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.xlim(-5, 10)\n", "plt.ylim(-10, 80)\n", "plt.axvline(x=5, color='purple', linestyle='--')\n", "plt.axhline(y=37, color='purple', linestyle='--')\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "AaRfRUYtcxZO"}, "source": ["$$\\lim_{x \\to 1} \\frac{x^2 - 1}{x - 1}$$"]}, {"cell_type": "code", "execution_count": 112, "metadata": {"id": "ZdDhpdiPcxZO"}, "outputs": [], "source": ["def my_fxn(my_x):\n", "    my_y = (my_x**2 - 1)/(my_x - 1)\n", "    return my_y"]}, {"cell_type": "code", "execution_count": 113, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5xKbepf445LM", "outputId": "8c8fac8a-9026-45d6-9476-906cd80111ad"}, "outputs": [{"data": {"text/plain": ["3.0"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["my_fxn(2)"]}, {"cell_type": "code", "execution_count": 114, "metadata": {"id": "umrgvf3BcxZQ"}, "outputs": [], "source": ["# Uncommenting the following line results in a 'division by zero' error:\n", "# my_fxn(1)"]}, {"cell_type": "code", "execution_count": 115, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yeGqX8oscxZS", "outputId": "041d14a7-d67c-4302-f05d-3527ade11ab2"}, "outputs": [{"data": {"text/plain": ["1.9"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["my_fxn(0.9)"]}, {"cell_type": "code", "execution_count": 116, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4unTKbu8cxZU", "outputId": "58b57aa7-7550-471e-bb3f-9827307db589"}, "outputs": [{"data": {"text/plain": ["1.9989999999999712"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["my_fxn(0.999)"]}, {"cell_type": "code", "execution_count": 117, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YuVfXKa5cxZW", "outputId": "4a63cee9-c029-477d-a7cb-fd805a08653b"}, "outputs": [{"data": {"text/plain": ["2.1"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["my_fxn(1.1)"]}, {"cell_type": "code", "execution_count": 118, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x4eY1qWEcxZY", "outputId": "1c1a6d44-0137-44f4-e7cf-cb5a701f5006"}, "outputs": [{"data": {"text/plain": ["2.0009999999999177"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["my_fxn(1.001)"]}, {"cell_type": "code", "execution_count": 119, "metadata": {"id": "pjUtPQwAcxZb"}, "outputs": [], "source": ["y = my_fxn(x)"]}, {"cell_type": "code", "execution_count": 120, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "Tg4Ow8k3cxZe", "outputId": "3a13c87a-7eb0-47da-d959-a1b4bdedb65c"}, "outputs": [{"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.xlim(-1, 5)\n", "plt.ylim(-1, 5)\n", "plt.axvline(x=1, color='purple', linestyle='--')\n", "plt.axhline(y=2, color='purple', linestyle='--')\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "VQWYjKjVcxZf"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "hc3PDahDcxZg"}, "source": ["$$\\lim_{x \\to 0} \\frac{\\text{sin } x}{x}$$"]}, {"cell_type": "code", "execution_count": 121, "metadata": {"id": "XDLZLagKcxZg"}, "outputs": [], "source": ["def sin_fxn(my_x):\n", "    my_y = np.sin(my_x)/my_x\n", "    return my_y"]}, {"cell_type": "code", "execution_count": 122, "metadata": {"id": "HU-B5ebBcxZi"}, "outputs": [], "source": ["# Uncommenting the following line results in a 'division by zero' error:\n", "# y = sin_fxn(0)"]}, {"cell_type": "code", "execution_count": 123, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YjIqAWnwcxZk", "outputId": "f9760170-6248-444d-c8a5-e2a817100298"}, "outputs": [{"data": {"text/plain": ["np.float64(0.9983341664682815)"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["sin_fxn(0.1)"]}, {"cell_type": "code", "execution_count": 124, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mmTAnugUcxZm", "outputId": "b73c423a-cd63-4fdd-aa72-8e0a922e9e28"}, "outputs": [{"data": {"text/plain": ["np.float64(0.9999998333333416)"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["sin_fxn(0.001)"]}, {"cell_type": "code", "execution_count": 125, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UFM_c1t_cxZp", "outputId": "5f082cc3-a1e1-4f3f-926f-b3a4a18b4490"}, "outputs": [{"data": {"text/plain": ["np.float64(0.9983341664682815)"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["sin_fxn(-0.1)"]}, {"cell_type": "code", "execution_count": 126, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xrSXdv0ScxZr", "outputId": "8c66240a-ec06-400f-ddf7-02e0d1c8fc48"}, "outputs": [{"data": {"text/plain": ["np.float64(0.9999998333333416)"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["sin_fxn(-0.001)"]}, {"cell_type": "code", "execution_count": 127, "metadata": {"id": "5nhhRmJTcxZs"}, "outputs": [], "source": ["y = sin_fxn(x)"]}, {"cell_type": "code", "execution_count": 128, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "bolWRmXmcxZu", "outputId": "f06aec8a-ffcd-4110-da53-41b517ab8b95"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.xlim(-10, 10)\n", "plt.ylim(-1, 2)\n", "plt.axvline(x=0, color='purple', linestyle='--')\n", "plt.axhline(y=1, color='purple', linestyle='--')\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "2OI8JVY6cxZw"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "ly5b30kjcxZx"}, "source": ["$$ \\lim_{x \\to \\infty} \\frac{25}{x} $$"]}, {"cell_type": "code", "execution_count": 129, "metadata": {"id": "-WbP-XFncxZy"}, "outputs": [], "source": ["def inf_fxn(my_x):\n", "    my_y = 25/my_x\n", "    return my_y"]}, {"cell_type": "code", "execution_count": 130, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C-35M67YcxZ3", "outputId": "c6f52cd5-d3a4-4fa5-b15e-79d6aa4e981a"}, "outputs": [{"data": {"text/plain": ["0.025"]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["inf_fxn(1e3)"]}, {"cell_type": "code", "execution_count": 131, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jZ88FSTfcxZ5", "outputId": "e7313985-aa06-4730-eab9-382cc307fd14"}, "outputs": [{"data": {"text/plain": ["2.5e-05"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["inf_fxn(1e6)"]}, {"cell_type": "code", "execution_count": 132, "metadata": {"id": "PwTJXJWWeDZT"}, "outputs": [], "source": ["y = inf_fxn(x)"]}, {"cell_type": "code", "execution_count": 133, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "AwwKi3RMeBjV", "outputId": "e364db18-17e4-4fc6-ea04-b667a5f9965e"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.xlim(-10, 10)\n", "plt.ylim(-300, 300)\n", "_ = ax.plot(x, y)"]}, {"cell_type": "code", "execution_count": 134, "metadata": {"id": "2Rjoo4OKbmnB"}, "outputs": [], "source": ["left_x = x[x<0]\n", "right_x = x[x>0]"]}, {"cell_type": "code", "execution_count": 135, "metadata": {"id": "sb9QiH6vcxZ0"}, "outputs": [], "source": ["left_y = inf_fxn(left_x)\n", "right_y = inf_fxn(right_x)"]}, {"cell_type": "code", "execution_count": 136, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "id": "vleskD0VcxZ7", "outputId": "a1e13c1c-378e-40de-d0b1-9c32569c78ee"}, "outputs": [{"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.xlim(-10, 10)\n", "plt.ylim(-300, 300)\n", "ax.plot(left_x, left_y, c='C0')\n", "_ = ax.plot(right_x, right_y, c='C0')"]}, {"cell_type": "markdown", "metadata": {"id": "bA2xg33LcxZ8"}, "source": ["**Exercises:**\n", "\n", "Evaluate the limits below using techniques from the slides or above.\n", "\n", "1. $$ \\lim_{x \\to 0} \\frac{x^2-1}{x-1} $$\n", "2. $$ \\lim_{x \\to -5} \\frac{x^2-25}{x+5} $$\n", "3. $$ \\lim_{x \\to 4} \\frac{x^2 -2x -8}{x-4} $$\n", "4. $$ \\lim_{x \\to -\\infty} \\frac{25}{x} $$\n", "5. $$ \\lim_{x \\to 0} \\frac{25}{x} $$"]}, {"cell_type": "markdown", "metadata": {"id": "zIhHIcmzLTQQ"}, "source": ["FYI: While not necessary for ML nor for this *ML Foundations* curriculum, the `SymPy` [symbolic mathematics library](https://www.sympy.org/en/index.html) includes a `limits()` method. You can read about applying it to evaluate limits of expressions [here](https://www.geeksforgeeks.org/python-sympy-limit-method/)."]}, {"cell_type": "markdown", "metadata": {"id": "AbAn1ChdcxZ9"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "e7cnhfOYcxZ9"}, "source": ["## Segment 2: Computing Derivatives with Differentiation"]}, {"cell_type": "markdown", "metadata": {"id": "CDn8BeCDcxZ9"}, "source": ["Let's bring back our ol' buddy $y = x^2 + 2x + 2$:"]}, {"cell_type": "code", "execution_count": 137, "metadata": {"id": "Dk2EXlqccxZ-"}, "outputs": [], "source": ["def f(my_x):\n", "    my_y = my_x**2 + 2*my_x + 2\n", "    return my_y"]}, {"cell_type": "code", "execution_count": 138, "metadata": {"id": "SLmyyYdEcxaA"}, "outputs": [], "source": ["y = f(x)"]}, {"cell_type": "code", "execution_count": 139, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "eNIlXWeQcxaC", "outputId": "0fef978e-f7ed-40cc-e353-2759a0297b73"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "h7C7nPEhcxaD"}, "source": ["Let's identify the slope where, say, $x = 2$."]}, {"cell_type": "markdown", "metadata": {"id": "DeVa3RrtcxaE"}, "source": ["First, let's determine what $y$ is: "]}, {"cell_type": "code", "execution_count": 140, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hz5oolNEcxaE", "outputId": "8dd78728-b4ed-49d6-fc05-f5a65bbc7bed"}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["f(2)"]}, {"cell_type": "markdown", "metadata": {"id": "7fkzBRR5cxaF"}, "source": ["Cool. Let's call this point $P$, which is located at (2, 10):"]}, {"cell_type": "code", "execution_count": 141, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "IOI2oxj2cxaG", "outputId": "f60d568f-c926-4a70-d5ca-2783fc50082c"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(2, 10) # new\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "PPCiF6CacxaI"}, "source": ["The _delta method_ uses the difference between two points to calculate slope. To illustrate this, let's define another point, $Q$ where, say, $x = 5$."]}, {"cell_type": "code", "execution_count": 142, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YAP0LJnxcxaI", "outputId": "7d340508-1c97-4d88-bcb9-cf48bdcac524"}, "outputs": [{"data": {"text/plain": ["37"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["f(5)"]}, {"cell_type": "code", "execution_count": 143, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "usfMMyTvcxaK", "outputId": "a11a27e7-704c-4819-b920-cd0813f8b86a"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(2, 10)\n", "plt.scatter(5, 37, c = 'orange', zorder=3) # new\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "kdony9sucxaM"}, "source": ["To find the slope $m$ between points $P$ and $Q$: \n", "$$m = \\frac{\\text{change in }y}{\\text{change in }x} = \\frac{\\Delta y}{\\Delta x} = \\frac{y_2 - y_1}{x_2 - x_1} = \\frac{37-10}{5-2} = \\frac{27}{3} = 9$$"]}, {"cell_type": "code", "execution_count": 144, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kgoU-_wzcxaM", "outputId": "a44c6af7-fe97-4a90-fafa-7228c5b44982"}, "outputs": [{"data": {"text/plain": ["9.0"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["m = (37-10)/(5-2)\n", "m"]}, {"cell_type": "markdown", "metadata": {"id": "ZWtSUfLVcxaP"}, "source": ["To plot the line that passes through $P$ and $Q$, we can rearrange the equation of a line $y = mx + b$ to solve for $b$: \n", "$$b = y - mx$$"]}, {"cell_type": "code", "execution_count": 145, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UuYXrltscxaP", "outputId": "4aa81301-d991-4288-d314-2ea828c8f53a"}, "outputs": [{"data": {"text/plain": ["-8.0"]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["b = 37-m*5\n", "b"]}, {"cell_type": "code", "execution_count": 146, "metadata": {"id": "AwawB1LLcxaR"}, "outputs": [], "source": ["line_y = m*x + b"]}, {"cell_type": "code", "execution_count": 147, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "Lkg2Bl7TcxaT", "outputId": "89e9c6a9-5e5b-45b7-8091-98a09f1ae21f"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(2, 10)\n", "plt.scatter(5, 37, c='orange', zorder=3)\n", "plt.ylim(-5, 150) # new\n", "plt.plot(x, line_y, c='orange') # new\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "RGEaUT_OcxaV"}, "source": ["The closer $Q$ becomes to $P$, the closer the slope $m$ comes to being the true tangent of the point $P$. Let's demonstrate this with another point $Q$ at $x = 2.1$."]}, {"cell_type": "markdown", "metadata": {"id": "xWAJUiEbcxaV"}, "source": ["Previously, our $\\Delta x$ between $Q$ and $P$ was equal to 3. Now it is much smaller: $$\\Delta x = x_2 - x_1 = 2.1 - 2 = 0.1 $$"]}, {"cell_type": "code", "execution_count": 148, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-XDEG_o7cxaV", "outputId": "6c6c7666-257b-405c-eaac-0cb721957e53"}, "outputs": [{"data": {"text/plain": ["10.61"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["f(2.1)"]}, {"cell_type": "code", "execution_count": 149, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "85n3ApTmcxaX", "outputId": "9ac2ffd7-8eaf-40c3-e815-d87ae6b783c3"}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAigAAAGdCAYAAAA44ojeAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAUaZJREFUeJzt3Xd8U+XiBvAnadJ0JqW7hbaUWWbZpUURpZchIiigIAoqggNQxJ+DexXcKHIVQa/gArwCCg4UULwsC0gpUDZtgUKhLd2UJt1Nk/f3RyBSKKMl6cl4vp9PPkpyEp5wCHn6nve8RyaEECAiIiKyIXKpAxARERFdiQWFiIiIbA4LChEREdkcFhQiIiKyOSwoREREZHNYUIiIiMjmsKAQERGRzWFBISIiIpujkDpAYxiNRuTk5MDb2xsymUzqOERERHQThBAoLS1FaGgo5PLrj5HYZUHJyclBWFiY1DGIiIioEbKystCiRYvrbmOXBcXb2xuA6Q2q1WqJ0xCRpRiNRqSlpQEAoqKibvgTFhHZF51Oh7CwMPP3+PXYZUG5dFhHrVazoBA5EKPRCC8vLwCmzzcLCpFjupnpGfz0ExERkc1hQSEiIiKbw4JCRERENocFhYiIiGwOCwoRERHZHBYUIiIisjksKERERGRzWFCIiIjI5rCgEBERkc1hQSEiIiKbw4JCRERENocFhYiIiGwOCwoRERGZHT2nxbSV+7HzZJGkOezyasZERERkHT8kZ2P94VzIZDLc1tZfshwcQSEiIiIAQE2tEb8cPAcAGNWjuaRZGlxQtm/fjuHDhyM0NBQymQxr1641P6bX6/Hyyy+jS5cu8PT0RGhoKCZMmICcnJw6r1FcXIzx48dDrVbDx8cHkyZNQllZ2S2/GSIiImq8rWkFuFChR5BahdvbBkiapcEFpby8HNHR0fj000+veqyiogL79+/Ha6+9hv379+Onn37C8ePHce+999bZbvz48Th27Bg2bdqE9evXY/v27ZgyZUrj3wURERHdsh+SswEAI7s3h4tcJmmWBs9BGTp0KIYOHVrvYxqNBps2bapz3yeffII+ffogMzMT4eHhSE1NxcaNG7F371706tULALBo0SLcfffdmD9/PkJDQxvxNoiIiOhWFJVV48/jBQCA0T1aSJymCeagaLVayGQy+Pj4AAASExPh4+NjLicAEB8fD7lcjqSkpHpfo7q6Gjqdrs6NiIiILGftgXOoNQpEh/mgbZC31HGsW1Cqqqrw8ssvY9y4cVCr1QCAvLw8BAYG1tlOoVDA19cXeXl59b7O3LlzodFozLewsDBrxiYiInIqQgis3pcFABjTU/rRE8CKBUWv1+OBBx6AEAKfffbZLb3WrFmzoNVqzbesrCwLpSQiIqLD2VqcyC+DSiHH8GjbmGphlXVQLpWTs2fPYuvWrebREwAIDg5GQUFBne1ra2tRXFyM4ODgel9PpVJBpVJZIyoREZHTW5Ns+sF/SOdgaNyVEqcxsfgIyqVycvLkSWzevBl+fn51Ho+NjUVJSQmSk5PN923duhVGoxExMTGWjkNERETXUaU34JeDpuVAxvS0nSkUDR5BKSsrQ3p6uvnXGRkZOHjwIHx9fRESEoLRo0dj//79WL9+PQwGg3leia+vL1xdXdGhQwcMGTIEkydPxuLFi6HX6zFt2jSMHTuWZ/AQERE1sT+O5aG0qhbNfdwR19rvxk9oIg0uKPv27cOdd95p/vXMmTMBABMnTsTrr7+OX3/9FQDQrVu3Os/btm0bBgwYAABYsWIFpk2bhoEDB0Iul2PUqFFYuHBhI98CERERNdaltU9G9WwBucRrn1yuwQVlwIABEEJc8/HrPXaJr68vVq5c2dDfmoiIiCzoXEkldqabLgpoK2fvXMJr8RARETmpH5OzIQQQ28oPYb4eUsepgwWFiIjICRmNwnx4Z0wv2xo9AVhQiIiInFJSRjEyiyvgpVJgaOcQqeNchQWFiIjICV1a+2R4dAjcXV0kTnM1FhQiIiInU1Zdi9+PmJYBGW1Da59cjgWFiIjIyWw4nINKvQGtAjzRI9xH6jj1YkEhIiJyMqv3mSbHPtArDDKZ7ax9cjkWFCIiIidyqrAMyWcvwEUuw/3dm0sd55pYUIiIiJzIpVOL72gXgEC1m8Rpro0FhYiIyEnUGoz4MfnS4R3bW/vkciwoRERETmLHySIUlFbD19MVd0UFSR3nulhQiIiInMT3e01rn4zoFgpXhW1XANtOR0RERBZRWFqNzan5AICxvcMlTnNjLChERERO4Mf92ag1CnQP90H7YG+p49wQCwoREZGDE0KYD++M7W2bK8deiQWFiIjIwSVlFCOjqByeri64p2uo1HFuCgsKERGRg7s0enJvt1B4qhQSp7k5LChEREQOTFuhx29HcgHYx+TYS1hQiIiIHNjag+dQXWtEVLA3urbQSB3nprGgEBEROSghBFbtyQRgmhxrqxcGrA8LChERkYM6nK1FWl4pXBVy3Nfdtpe2vxILChERkYP67uLk2Ls7B0PjoZQ4TcOwoBARETmg8upa/HrwHABgbB/7mRx7CQsKERGRA9pwOBflNQZE+nsiJtJX6jgNxoJCRETkgFbtNU2OfdDOJsdewoJCRETkYI7nleJAZgkUchlG9bCvybGXsKAQERE5mEsrx8Z3CEKAt0riNI3DgkJERORAqvQG/HQgGwDwYB/7uDBgfVhQiIiIHMj/UvJRUqFHqMYN/dsGSB2n0VhQiIiIHMh3F1eOHdMrDC5y+5scewkLChERkYPIKCrHrlPnIZMBY3rZ5+TYS1hQiIiIHMSl6+7c2T4QLZp5SJzm1rCgEBEROYDqWgPW7DOdvfOQHa4ceyUWFCIiIgew8WgeLlycHHtnVKDUcW4ZCwoREZEDWLHbdHhnbJ9wu54cewkLChERkZ07kV+KPWeK4SKX4cHe9rv2yeVYUIiIiOzcyiTT6El8h0AEqd0kTmMZLChERER2rLLGgB/3m1aOHR8TIXEay2FBISIismPrDuegtKoW4b4euK2Nv9RxLIYFhYiIyI5dOrwzrk845A4wOfYSFhQiIiI7dfScFgezSqB0kdn9yrFXYkEhIiKyUysvrhw7uFMw/L1UEqexLBYUIiIiO1RWXYtfDpwD4FiTYy9hQSEiIrJDvxw8h/IaA1oFeKJvK1+p41hcgwvK9u3bMXz4cISGhkImk2Ht2rV1HhdCYPbs2QgJCYG7uzvi4+Nx8uTJOtsUFxdj/PjxUKvV8PHxwaRJk1BWVnZLb4SIiMhZCCHMK8c+1CccMpnjTI69pMEFpby8HNHR0fj000/rfXzevHlYuHAhFi9ejKSkJHh6emLw4MGoqqoybzN+/HgcO3YMmzZtwvr167F9+3ZMmTKl8e+CiIjIiRzK1iIlVwdXhRyjezrW5NhLFA19wtChQzF06NB6HxNCYMGCBXj11VcxYsQIAMA333yDoKAgrF27FmPHjkVqaio2btyIvXv3olevXgCARYsW4e6778b8+fMRGhp6C2+HiIjI8a3YfRYAcE/XEPh4uEqcxjosOgclIyMDeXl5iI+PN9+n0WgQExODxMREAEBiYiJ8fHzM5QQA4uPjIZfLkZSUVO/rVldXQ6fT1bkRERE5I22lHusO5wAAxseES5zGeixaUPLy8gAAQUFBde4PCgoyP5aXl4fAwLqXgVYoFPD19TVvc6W5c+dCo9GYb2FhjnEhJCIioob6MTkbVXojooK90SO8mdRxrMYuzuKZNWsWtFqt+ZaVlSV1JCIioiZnNAr89+LhnYf7Rjjk5NhLLFpQgoODAQD5+fl17s/Pzzc/FhwcjIKCgjqP19bWori42LzNlVQqFdRqdZ0bERGRs9mZXoSMonJ4qxS4r3tzqeNYlUULSmRkJIKDg7FlyxbzfTqdDklJSYiNjQUAxMbGoqSkBMnJyeZttm7dCqPRiJiYGEvGISIicijfJJpGT0b1bAFPVYPPc7ErDX53ZWVlSE9PN/86IyMDBw8ehK+vL8LDwzFjxgy8/fbbaNu2LSIjI/Haa68hNDQUI0eOBAB06NABQ4YMweTJk7F48WLo9XpMmzYNY8eO5Rk8RERE15B9oQJb00xHKB7u63grx16pwQVl3759uPPOO82/njlzJgBg4sSJWLZsGV566SWUl5djypQpKCkpwW233YaNGzfCzc3N/JwVK1Zg2rRpGDhwIORyOUaNGoWFCxda4O0QERE5phVJmTAKoF8bP7QJ9JI6jtXJhBBC6hANpdPpoNFooNVqOR+FyIEYjUakpKQAADp27Ai53C7m8RNZXZXegLj3tqK4vAaLH+6JIZ3rn7Np6xry/c1PPxERkY377UguistrEKpxQ3yHwBs/wQGwoBAREdm4S5NjH4oJh8LFOb66neNdEhER2akj2VoczCqB0kWGB3s77sqxV2JBISIismHfJJ4BANzdJQQB3ippwzQhFhQiIiIbdaG8Br8eMl13Z0Ks459afDkWFCIiIhu1el8WqmuN6Biidujr7tSHBYWIiMgGGYwC3yaZJsdOjHPs6+7UhwWFiIjIBiWcKEBWcSU07krcG+3Y192pDwsKERGRDbp0avGYni3g7uoicZqmx4JCRERkY86eL0fCiUIAznHdnfqwoBAREdmY/yaehRDAHe0C0NLfU+o4kmBBISIisiHl1bX4fl8WANPkWGfFgkJERGRDftqfjdKqWkT6e2JAO+e47k59WFCIiIhshNEosHTXGQDAxNgIyOXOdWrx5VhQiIiIbMSO9CKcLiyHl0qB0b3CpI4jKRYUIiIiG7H0rwwAwJheLeClUkicRlosKERERDbgVGEZ/jxeCJkMeDSupdRxJMeCQkREZAOWX5x7MjAqEBF+znlq8eVYUIiIiCSmq9Ljh+RsAMBj/SIlTmMbWFCIiIgktnpvFipqDGgX5IW41n5Sx7EJLChEREQSMhgFlieeAQA8GhfpdFctvhYWFCIiIgltSc1HVnElfDyUuK+78121+FpYUIiIiCS07OLk2LG9w53yqsXXwoJCREQkkbQ8HXadOg8XuQyPxDrvdXfqw4JCREQkkWV/nQEADO4UhOY+7tKGsTEsKERERBK4UF6Dnw+cA8BTi+vDgkJERCSBVXszUV1rROfmavSKaCZ1HJvDgkJERNTEamqN5pVjH+OpxfViQSEiImpiG47kIF9XjUBvFYZHh0odxyaxoBARETUhIQS+2G66avHEuJZwVfCruD78UyEiImpCiafPIyVXB3elC8bHhEsdx2axoBARETWhL3eYRk9G92wBHw9XidPYLhYUIiKiJpJeUIataQWQyYDHb+OpxdfDgkJERNREvtppGj2J7xCESH9PidPYNhYUIiKiJnC+rBo/7c8GAEy+vZXEaWwfCwoREVET+Ha3aWG2ri006N2SC7PdCAsKERGRlVXpDfjv7jMAgCdub8WF2W4CCwoREZGV/XLwHIrKahCqccPQzsFSx7ELLChERERWJIQwn1r8WL9IKF341Xsz+KdERERkRQknCnGyoAxeKgUe7BMmdRy7wYJCRERkRZdGTx7sHQa1m1LiNPaDBeUyZdW1+HLHaew9Uyx1FCIicgCpuTrsTC+CXAY8GtdS6jh2hQXlMh9vPoG3N6Ri4ZaTUkchIiIHcGn0ZGiXEIT5ekicxr6woFxmQmxLuMhl2HGyCEfPaaWOQ0REdixXW4lfDp4DADzBZe0bzOIFxWAw4LXXXkNkZCTc3d3RunVrvPXWWxBCmLcRQmD27NkICQmBu7s74uPjcfKk9KMWYb4euKdrCABgyfbTEqchIiJ79tWODNQaBWIifdE9nAuzNZTFC8r777+Pzz77DJ988glSU1Px/vvvY968eVi0aJF5m3nz5mHhwoVYvHgxkpKS4OnpicGDB6OqqsrScRpsSn/T8sMbDucgq7hC4jRERGSPtBV6rNqTCQB4akBridPYJ4sXlF27dmHEiBEYNmwYWrZsidGjR2PQoEHYs2cPANPoyYIFC/Dqq69ixIgR6Nq1K7755hvk5ORg7dq1lo7TYJ1CNejfLgBGAXyxg6MoRETUcN8mnUV5jQFRwd4Y0C5A6jh2yeIFJS4uDlu2bMGJEycAAIcOHcLOnTsxdOhQAEBGRgby8vIQHx9vfo5Go0FMTAwSExPrfc3q6mrodLo6N2t66uIoyup9WThfVm3V34uIiBxLld6ApX+ZJsc+eQeXtW8sixeUV155BWPHjkVUVBSUSiW6d++OGTNmYPz48QCAvLw8AEBQUFCd5wUFBZkfu9LcuXOh0WjMt7Aw6y50E9vaD12aa1ClN2J54lmr/l5ERORYftyfjaKyGjT3ccc9XUOljmO3LF5QVq9ejRUrVmDlypXYv38/li9fjvnz52P58uWNfs1Zs2ZBq9Wab1lZWRZMfDWZTIan7jAdM/wm8Qwqamqt+vsREZFjMBgFvrh4ksWk27is/a1QWPoFX3zxRfMoCgB06dIFZ8+exdy5czFx4kQEB5sukpSfn4+QkBDz8/Lz89GtW7d6X1OlUkGlUlk66nUN6RyMCD8PnD1fge/3ZuGxfjxFjIiIru+PY3k4c74CGnclHuzNZe1vhcWrXUVFBeTyui/r4uICo9EIAIiMjERwcDC2bNliflyn0yEpKQmxsbGWjtNoLnIZJt9umovy5Y4M6A1GiRMREZEtE0JgScIpAMDE2Ah4qiw+BuBULF5Qhg8fjnfeeQcbNmzAmTNn8PPPP+PDDz/EfffdB8B0+GTGjBl4++238euvv+LIkSOYMGECQkNDMXLkSEvHuSWje7aAv5crzpVU4rcjuVLHISIiG5Z4+jwOZWuhUsgxgcva3zKL17tFixbhtddewzPPPIOCggKEhobiySefxOzZs83bvPTSSygvL8eUKVNQUlKC2267DRs3boSbm5ul49wSN6ULHo1rifn/O4HFCadxb3QoZ2MTEVG9liSY5p480CsM/l5NOy3BEcnE5Uu82gmdTgeNRgOtVgu1Wm3V36ukogZx721FRY0Byx/vgzt4PjuR1RiNRqSkpAAAOnbseNXhYiJblZKjw90Ld0AuA/78vzsR7sfr7tSnId/f/PTfgI+HK8b2DgcALP7zlMRpiIjIFn2+3fT9cHeXEJYTC2FBuQmTbo+EQi4zHV/MKpE6DhER2ZCs4gqsO2yap3hpiQq6dSwoN6G5jzvujTYttrM4gaMoRET0t692ZsBgFLitjT86N9dIHcdhsKDcpCcvtuKNx/KQXlAqcRoiIrIFRWXV+G6v6aKAT97RSuI0joUF5Sa1D/bGPzoGQQjgP5yLQkREMI2eVOmNiG6hwW1t/KWO41BYUBpg2p1tAAC/HMxBVnGFxGmIiEhK2go9/nvxem1T72zDZSgsjAWlAaLDfHB7W38YjAJLtnMUhYjImS1PPIOy6lq0D/JGfIegGz+BGoQFpYGmXhxFWb0vGwW6KonTEBGRFMqra/H1XxkAgGfubA25nKMnlsaC0kAxkb7oFdEMNbVGfLHjtNRxiIhIAqv2ZKKkQo+Wfh64p2uo1HEcEgtKA8lkMvMoyoqkTFwor5E4ERERNaUqvQGfbzf9gPrUHa3hwtETq2BBaYQB7QPQKVSNihoDll4c4iMiIufwQ3I2CkqrEaJxw/09Wkgdx2GxoDTC5aMoy3adQWmVXuJERETUFGoNRvOCnVP6t4Krgl+j1sI/2UYa0ikYrQM8oauqxbe7M6WOQ0RETeDXQznIvlAJP8+/r9NG1sGC0khyuQzPDDCNony18zSq9AaJExERkTUZjcK8UOek2yPh7uoicSLHxoJyC+7tFooWzdxRVFaD7/ZwFIWIyJH9cSwP6QVl8HZT4JG+EVLHcXgsKLdA6SI3X6NnyfbTqKk1SpyIiIisQQiBT7alAwAejWsJbzelxIkcHwvKLRrTswUCvVXI1Vbh5wPZUschIiIrSDhRiGM5OrgrXfBYv0ip4zgFFpRb5KZ0weTbTVew/M+fp1Br4CgKEZEjEUJg4ZaTAIDxMeHw9XSVOJFzYEGxgPF9w+Hn6Yqz5yuw9mCO1HGIiMiCdpwswv7MEqgUcky5o5XUcZwGC4oFeLgqMLm/6S/toq0nOYpCROQghBD42Dx6EoFAbzeJEzkPFhQLeaRvBHwvjqL8wlEUIiKH8Ff6eSSfvQCVQo6nOHrSpFhQLMRTpTDPRflkWzpHUYiI7Jxp9OQEAGBcn3AEqjl60pRYUCxoQmwEmnkokVFUjl8PcRSFiMie7Tp1HnvPXICrQo6nB7SWOo7TYUGxIE/V33NRPtmaDoNRSJyIiIgaQwiBjzeb5p481CccQRw9aXIsKBY2IbYlfDyUOF1UjnUcRSEiskuJp89jz5liuLrI8dQdHD2RAguKhXldNhdl4daTHEUhIrJDl0ZPxvUJQ7CGoydSYEGxggmxEaZRlMJyrD/MURQiInuSeOo8kjIujp5w7olkWFCswNtNiSduMy2FvHALR1GIiOzJpTN3HuwdhhCNu8RpnBcLipVMjGsJjbsSpziKQkRkN5JOn8fu06bRE565Iy0WFCu5fBRlEc/oISKyC5dWjX2gdwuE+nD0REosKFY0sV9LqN0USC8ow4YjuVLHISKi60g6fR67Tp2H0kWGpwe0kTqO02NBsSK1mxJPXDyjZ8HmE1xdlojIRgkh8O//meaePNArDM05eiI5FhQre6xfS/MZPbzSMRGRbdpxssi07olCjul3tZU6DoEFxeq83ZTmRX4+3nICNbUcRSEisiWm0ZPjAEwXfuW6J7aBBaUJTIxtCX8vFbKKK7EmOUvqOEREdJnNqQU4lK2Fu9KFZ+7YEBaUJuDu6oJpd5r+0i/ako4qvUHiREREBABG49+jJ4/1M/0wSbaBBaWJjIsJR6jGDXm6KqxMypQ6DhERAfjtaC7S8krhrVJgysWLvZJtYEFpIiqFC6YPNE28+s+f6aioqZU4ERGRc6s1GPHhJtOZO0/c3go+Hq4SJ6LLsaA0odE9WyDCzwNFZTVYtuuM1HGIiJzaLwdzcLqwHD4eSjx+W0up49AVWFCakNJFjhnxplGUJQmnoavSS5yIiMg56Q1GLLh4zZ0n+7eGt5tS4kR0JRaUJnZvdHO0CfSCtlKPr3ZkSB2HiMgprdmXjaziSvh7uWJiXITUcageLChNzEUuw8x/tAMAfLUzAxfKayRORETkXKr0BizaarrmzjMD2sDDVSFxIqoPC4oEhnQKRscQNcqqa7Fk+2mp4xAROZWVSZnI1VYhWO2Gh2LCpY5D18CCIgG5XIYXBplGUZbtykBBaZXEiYiInEN5dS3+82c6AGD6wDZwU7pInIiuxSoF5dy5c3j44Yfh5+cHd3d3dOnSBfv27TM/LoTA7NmzERISAnd3d8THx+PkyZPWiGKz7ooKRPdwH1TpjVi0JV3qOERETuHLHRkoKqtBhJ8HxvQMkzoOXYfFC8qFCxfQr18/KJVK/P7770hJScG///1vNGvWzLzNvHnzsHDhQixevBhJSUnw9PTE4MGDUVXlPCMJMpkMLw+JAgCs2pOJM0XlEiciInJs58uq8fn2UwCAFwa1h6uCBxFsmcX3zvvvv4+wsDAsXboUffr0QWRkJAYNGoTWrU1LvQshsGDBArz66qsYMWIEunbtim+++QY5OTlYu3atpePYtL6t/DCgfQBqjQLzLy61TERE1vHJtnSU1xjQKVSNe7qESB2HbsDiBeXXX39Fr169MGbMGAQGBqJ79+744osvzI9nZGQgLy8P8fHx5vs0Gg1iYmKQmJhY72tWV1dDp9PVuTmKlwZHQSYD1h/OxZFsrdRxiIgcUlZxBVbsNl1m5OUhUZDLZRInohuxeEE5ffo0PvvsM7Rt2xZ//PEHnn76aTz77LNYvnw5ACAvLw8AEBQUVOd5QUFB5seuNHfuXGg0GvMtLMxxjht2DFVjZLfmAID3N6ZJnIaIyDF9tOkEagxGxLX2w+1t/aWOQzfB4gXFaDSiR48eePfdd9G9e3dMmTIFkydPxuLFixv9mrNmzYJWqzXfsrKyLJhYejP/0Q5KFxl2phdh58kiqeMQETmU1Fwdfj54DoBp9EQm4+iJPbB4QQkJCUHHjh3r3NehQwdkZpqG1oKDgwEA+fn5dbbJz883P3YllUoFtVpd5+ZIwnw9MD7GtJLh+xvTYDQKiRMRETmOD/44DiGAu7sEIzrMR+o4dJMsXlD69euH48frTvg8ceIEIiJMX8CRkZEIDg7Gli1bzI/rdDokJSUhNjbW0nHsxvS72sBLpcCRc1psOJIrdRwiIoewJ6MYW9MK4CKX4f8GtZc6DjWAxQvK888/j927d+Pdd99Feno6Vq5cic8//xxTp04FYDq9dsaMGXj77bfx66+/4siRI5gwYQJCQ0MxcuRIS8exG35eKky+vRUA4N//Ow69wShxIiIi+yaEwHu/pwIAHugVhlYBXhInooaweEHp3bs3fv75Z6xatQqdO3fGW2+9hQULFmD8+PHmbV566SVMnz4dU6ZMQe/evVFWVoaNGzfCzc3N0nHsyhO3R8LfyxVnzlfgu72ONc+GiKipbUrJx/7MErgp/76SPNkPmRDC7iY86HQ6aDQaaLVah5uP8k3iGcz+5Rj8vVRIeHEAPFW8iBU5D6PRiJSUFABAx44dIZdzIS1qnFqDEUM+3oH0gjI8M6A1Xrq4MCZJqyHf3/z025ixvcMR7uuBorJqfL0zQ+o4RER26cf92UgvKIPGXYkn72gtdRxqBBYUG+OqkJsvJLhk+2kUlVVLnIiIyL6UV9fi3/87AQCYdmcbaNyVEieixmBBsUHDu4aiawsNyqpr8dGmE1LHISKyK1/sOI2C0mqE+bpjQlyE1HGokVhQbJBcLsO/7u4AwHQhwZP5pRInIiKyDwW6KixJOA3AtCibSuEicSJqLBYUGxXTyg+DOgbBKIB3f0uVOg4RkV349/9OoFJvQPdwHwzjBQHtGguKDXtlaBQUchm2HS/kEvhERDeQmqvD6mTTEg2vDuvAJe3tHAuKDWsV4IWH+5qOn769IQUGLoFPRHRN7/6Wal7SvmeEr9Rx6BaxoNi4Zwe2hbebAml5pfhxf7bUcYiIbFLCiULsOFkEpYsML3PNE4fAgmLjfD1dMf2uNgCA+X8cR0VNrcSJiIhsi8Eo8O4G01y9CbEtEeHnKXEisgQWFDswMa4lwnzdUVBajc+3n5Y6DhGRTVmzLwvH80uhcVeaf6Aj+8eCYgdUChfzkOWShNPI11VJnIiIyDaUV9fi3xfXi5p+Vxv4eLhKnIgshQXFTgzrEoLu4T6o1Bvw4f+4eBsREWBacbuwtBrhvh54JJaLsjkSFhQ7IZPJ8Oow0+Jtq5OzkJKjkzgREZG0ckoq8fn2UwC4KJsjYkGxIz0jfDGsSwiEAN5cfwx2eCFqIiKLmft7Gqr0RvRp6Yu7uwRLHYcsjAXFzrwyNAoqhRy7Txdj49E8qeMQEUliT0Yx1h3KgUwGzB7ekYuyOSAWFDsT5uuBJ/u3AgC8vSEVVXqDxImIiJqWwSjwxrpjAICxvcPQublG4kRkDSwoduipAa0RonHDuZJKfMHTjonIyfyQnIVjOTp4qxR4YVB7qeOQlbCg2CEPVwVeGWo67fg/f55CrrZS4kRERE1DV6XHB38cBwA8F98W/l4qiRORtbCg2Kl7o0PRu2UzVOoNeO/3NKnjEBE1iU+2pqOorAatAjwxIbal1HHIilhQ7JRMJsOc4Z0gkwG/HMzBvjPFUkciIrKq04VlWPpXBgDgtXs6wlXBrzBHxr1rxzo312Bs7zAAwOvrjvFqx0Tk0N7ekAq9QeDO9gG4s32g1HHIylhQ7NwLg9rD202Bo+d0+CE5S+o4RERWse14AbamFUAhl+HVezpKHYeaAAuKnfP3UuG5gW0BAB/8cRy6Kr3EiYiILEtvMOKt9SkAgEfjWqJ1gJfEiagpsKA4gIlxLdE6wBNFZTVYuPmk1HGIiCxq6V8ZOF1YDj9PV0y/+AMZOT4WFAegdJHjtYtDnst2ncGJ/FKJExERWUauthILLv7g9fLQKGjclRInoqbCguIgBrQPxKCOQag1Cry29iiv00NEDuHtDamoqDGgR7gPRvdoIXUcakIsKA5k9vCOcFPKkZRRjF8O5kgdh4joluw8WYQNh3MhlwFvjewMuZzX23EmLCgOpEUzD0y/y3R89u0NqZwwS0R2q6bWiNm/HgUATIhtiU6hvN6Os2FBcTBP3B6JVv6eKCqrxof/OyF1HCKiRvlqp2lirL+XK57/Rzup45AEWFAcjErhgjdHdAYAfJN4BsdytBInIiJqmHMllVi4xTQxdtbQDpwY66RYUBzQbW39MaxrCIwCeG3tURi5wiwR2ZG316egUm9A75bNcH+P5lLHIYmwoDio14Z1hKerC/ZnluCH5Gyp4xAR3ZSEE4X4/WgeXOQyvDmiM2QyTox1ViwoDipY44YZ8abjtu9tTENJRY3EiYiIrq+61oDXfz0GAJgY2xIdQtQSJyIpsaA4sEf7tUS7IC8Ul9dg3h/HpY5DRHRdnyecRkZROQK8VXj+H1wx1tmxoDgwpYvcPGF21Z5MHMi8IHEiIqL6nS4sw6Jt6QCAV4d1gLcbJ8Y6OxYUB9e3lR/u79EcQgCzfjoCvcEodSQiojqEEPjXz0dRU2vE7W39cW90qNSRyAawoDiBf93dAc08lEjLK8WXOzKkjkNEVMeP+88h8fR5uCnleGdkF06MJQAsKE7Bz0uFfw0zXUxwweYTOHu+XOJEREQmxeU1eGdDCgDguYHtEO7nIXEishUsKE5iVI/miGvth+paI17lxQSJyEa8syEVFyr0iAr2xhO3R0odh2wIC4qTkMlkeOe+LnBVyLHjZBHWHjwndSQicnK70ovw4/5syGTAu/d3gdKFX0n0N/5tcCKR/p54bqDp1L231qfiQjnXRiEiaVTpDfjXWtPFAB+OiUCP8GYSJyJbw4LiZCbf3sq8Nso7v6VKHYeInNSn29KRUVSOQG8VXhzSXuo4ZINYUJyMq0KOufd3hUwG/JCcjV3pRVJHIiInczK/FIsTTgEA3ri3E9Rc84TqwYLihHpGNMP4mHAAwL/WHkWV3iBxIiJyFgajwCs/HYHeIBDfIRBDOgdLHYlslNULynvvvQeZTIYZM2aY76uqqsLUqVPh5+cHLy8vjBo1Cvn5+daOQpd5aUgUAr1VyCgqN1/WnIjI2pbvOoPksxfgpVLgDV4MkK7DqgVl7969WLJkCbp27Vrn/ueffx7r1q3DmjVrkJCQgJycHNx///3WjEJXULspzcvgL9l+GkeytRInIiJHl3m+Ah9cvC7YrLuj0NzHXeJEZMusVlDKysowfvx4fPHFF2jW7O/Z2VqtFl999RU+/PBD3HXXXejZsyeWLl2KXbt2Yffu3daKQ/UY0jkYw7qGwGAUePGHQ6ip5TL4RGQdQgi8/ONhVOoN6NvKF+N6h0sdiWyc1QrK1KlTMWzYMMTHx9e5Pzk5GXq9vs79UVFRCA8PR2JiYr2vVV1dDZ1OV+dGlvHmvZ3g6+mKtLxSfHrxQl1ERJa2ak+WeTn790d1hVzOQzt0fVYpKN999x3279+PuXPnXvVYXl4eXF1d4ePjU+f+oKAg5OXl1ft6c+fOhUajMd/CwsKsEdsp+Xmp8Pq9nQCYTvtLzWX5IyLLyimpxLsXlzV4cXAUIvw8JU5E9sDiBSUrKwvPPfccVqxYATc3N4u85qxZs6DVas23rKwsi7wumQzvGoJBHYNQe/FQD694TESWIoTAP38+grLqWvQI98GjcS2ljkR2wuIFJTk5GQUFBejRowcUCgUUCgUSEhKwcOFCKBQKBAUFoaamBiUlJXWel5+fj+Dg+k83U6lUUKvVdW5kOTKZDG+P7AyNuxJHz+nw+fbTUkciIgfx84Fz+PN4IVxd5Jg3uitceGiHbpLFC8rAgQNx5MgRHDx40Hzr1asXxo8fb/5/pVKJLVu2mJ9z/PhxZGZmIjY21tJx6CYFqt0w+x7TFY8/3nwSJ/NLJU5ERPauoLQKb6y7eKXi+LZoE+gtcSKyJwpLv6C3tzc6d+5c5z5PT0/4+fmZ7580aRJmzpwJX19fqNVqTJ8+HbGxsejbt6+l41AD3N+jOdYfzsG244V48YfD+PHpOP60Q0SNIoTAqz8fhbZSj87N1ZjSv5XUkcjOSLKS7EcffYR77rkHo0aNQv/+/REcHIyffvpJiih0GZlMhnfv7wJvlQIHs0rw5Q4e6iGixvlp/zn8LyUfShcZ5o2K5pWKqcFkQgghdYiG0ul00Gg00Gq1nI9iBd/vzcTLPx6Bq4sc66bfhvbBHJalpmE0GpGSYjok0LFjR8jl/FKzRzkllRj80XaUVtfixcHtMfXONlJHIhvRkO9vfvrpKg/0CsNdUYGoMRjx/PcHuYAbEd0048WzAUura9E93AdP8tAONRILCl1FJpPhvVFd0MxDiZRcHT7eckLqSERkJ/67+yz+SjctyPbvMdFQ8NAONRL/5lC9Ar3d8O59XQAAn/15CslnL0iciIhs3enCMsz93bQg26yhHdAqwEviRGTPWFDomoZ2CcF93ZvDKIAXVh9ERU2t1JGIyEbVGoyYufoQqvRG3NbGH4/0jZA6Etk5FhS6rtfv7YQQjRvOnK/A3N/SpI5DRDZqyfbTOJhVAm83BeaN5rV26NaxoNB1adyV+GB0NADTseWEE4USJyIiW3MsR4sFm01z1V4f3gmhPu4SJyJHwIJCN3RbW3/z9TNe+uEQSipqpA1ERDajSm/A898fhN4gMLhTEO7v0VzqSOQgWFDoprw8JAqtAjyRr6vGv9YehR0un0NEVvDub6k4kV8Gfy8V3rmvC2QyHtohy2BBoZvi7uqCjx7oBoVchg2Hc7EmOVvqSEQksS2p+fgm8SwA4N8PRMPfSyVxInIkLCh006LDfDBzUDsAwOu/HsPpwjKJExGRVAp0VXjxh8MAgEm3ReKOdgESJyJHw4JCDfJU/9aIa+2HihoDnv3uAKprDVJHIqImZjQKvLDmEIrLa9AhRI2XhrSXOhI5IBYUahC5XIYPH+iGZh5KHD2nw7//x1VmiZzN139lYMfJIrgp5Vg4thtUChepI5EDYkGhBgvWuOH9UV0BAJ9vP43tPPWYyGkcy9Fi3sbjAIBXh3VE2yBeTJSsgwWFGmVQp2DzSpEzVx9CUVm1xImIyNoqawx4dtUB1BiM+EfHIIyPCZc6EjkwFhRqtH8N64B2QV4oKqvGi2sO8dRjIgf35vpjOFVYjkBvFd4f1ZWnFJNVsaBQo7kpXbBwXHe4KuTYdrwQX/91RupIRGQlvxw8h1V7siCTAR8+0A2+nq5SRyIHx4JCtyQqWI1Xh3UAALz3eyoOZPKqx0SO5lRhGf750xEAwPQ72+C2tv4SJyJnwIJCt+yRvhG4u0sw9AaBaSsPcCl8IgdSpTdg6or9KK8xoG8rXzwX307qSOQkWFDolslkMrw3qisi/DxwrqQSL6w+BKOR81GIHMEb644hLa8U/l6uWDi2O1x4lWJqIiwoZBFqNyU+fagHXBVybEkrwBc7TksdiYhu0doDf887WfBgdwSq3aSORE6EBYUspnNzDeYM7wgAmPfHcew7UyxxIiJqrFOFZfjnzxfnndzVlvNOqMmxoJBFPdQnHPdGh8JgNM1HOc/1UYjszqV5JxWX5p0MbCt1JHJCLChkUTKZDO/e3wWtAjyRp6vC85yPQmRXhBB4de1RzjshybGgkMV5qRT4z/gecFPKsf1EIRZtTZc6EhHdpBVJmfghORtyGfDxWM47IemwoJBVRAWr8daIzgCABVtOYEtqvsSJiOhGks9ewBvrjgEAXhoShX5tOO+EpMOCQlYzplcYHu4bDiGAGd8fREZRudSRiOgaCkur8cyKZOgNAkM7B+PJ/q2kjkROjgWFrGr2PZ3QM6IZSqtqMeWbfSivrpU6EhFdQW8wYurK/cjXVaNNoBc+GBPN6+yQ5FhQyKpcFXJ8Nr4HAr1VOFlQhhd/4EUFiWzNe7+nYU9GMbxUCix5pCe8VAqpIxGxoJD1Bard8NnDPaB0keG3I3lYnMBF3IhsxS8Hz+GrnRkAgPljotE6wEviREQmLCjUJHpG+GLO8E4AgA/+SMOOk4USJyKiYzlavPKjaTG2Zwa0xpDOwRInIvobCwo1mfEx4XigVwsYBTB91QFknq+QOhKR0yosrcbk5ftQqTfg9rb+eGFQe6kjEdXBgkJNRiaT4c0RnRHdQoOSCj0mLd8LXZVe6lhETqe61oCnvk1GjrYKrfw98cm4HlyMjWwOCwo1KTelCz6f0AtBatOk2ekrD6DWYJQ6FpHTEELgXz8fRfLZC1C7KfDlxF7QeCiljkV0FRYUanJBajd8MaEX3JRyJJwoxLu/pUkdichpfLkjAz8kZ8NFLsOn43ugFSfFko1iQSFJdG3hg3+P6QYA+PqvDKzakyltICInsC2tAO/+ngoAeG1YB9zeNkDiRETXxoJCkhnWNQQz/9EOAPDa2qNIPHVe4kREjutkfimmrzoAIYBxfcIxMa6l1JGIrosFhSQ1/a42GB4dilqjwNMrknGGy+ETWVxhaTUeX74XZdW1iIn0xRv3duJKsWTzWFBIUjKZDB+M7mo+s+fx5XtRUlEjdSwih1FRU4snlu9FVnElIvw88NnDPeGq4D/9ZPv4t5Qk56Z0wRcTeiFU44bTheWY/M0+VOkNUscisnsGo8Czqw7iULYWzTyUWPZYH/h6ukodi+imsKCQTQhUu2HpY33g7abA3jMX8MKaQzAaec0eosYSQuCt9SnYnJoPV4UcX07shUh/T6ljEd00FhSyGe2DvbHkkZ5Qusiw4XAu5l4824CIGu6rnRlYtusMAOCjB7qhZ4SvtIGIGogFhWxKXGt/fDA6GgDwxY4MLP0rQ+JERPbn9yO5eOc3U8H/591RGNY1ROJERA3HgkI2Z2T35nhxsOm6IG+uT8HGo7kSJyKyH/vOFGPG9wchBPBI3whMvr2V1JGIGoUFhWzSMwNa46GYcAgBPPfdQew7Uyx1JCKbl5anw+PL9qK61oiBUYGYM7wjTycmu2XxgjJ37lz07t0b3t7eCAwMxMiRI3H8+PE621RVVWHq1Knw8/ODl5cXRo0ahfz8fEtHITsmk8nw5r2dMDAqENW1Rjy2bC9ScnRSxyKyWVnFFZjw1R7oqmrRM6IZPnmoBxQu/BmU7JfF//YmJCRg6tSp2L17NzZt2gS9Xo9BgwahvPzvBbief/55rFu3DmvWrEFCQgJycnJw//33WzoK2TmFixyfPNQDvSKaobSqFhO+3sOF3IjqUVhajYe/SkJBaTXaB3nj64m94e7qInUsolsiE0JY9VzOwsJCBAYGIiEhAf3794dWq0VAQABWrlyJ0aNHAwDS0tLQoUMHJCYmom/fvjd8TZ1OB41GA61WC7Vabc34ZAO0lXqM/Xw3UnN1aNHMHT88FYdgjZvUscgKjEYjUlJSAAAdO3aEXM4RgBvRVenx4JK/Px8/Ph2HIDU/H2SbGvL9bfVPv1arBQD4+ppOcUtOToZer0d8fLx5m6ioKISHhyMxMbHe16iuroZOp6tzI+ehcVfim8f7oKWfB7IvVOKRr5JwoZyrzRJV6Q14Yvk+pObq4O/lim8nxbCckMOwakExGo2YMWMG+vXrh86dOwMA8vLy4OrqCh8fnzrbBgUFIS8vr97XmTt3LjQajfkWFhZmzdhkgwK8VfjvpBgEqVU4WVCGR5eZritC5Kxqao2YumI/9mQUw1ulwPLH+6AlF2IjB2LVgjJ16lQcPXoU33333S29zqxZs6DVas23rKwsCyUkexLm64FvJ8XAx0OJQ1klmMIl8clJ1RqMeO67A9iSVgCVQo4vJvZCp1CN1LGILMpqBWXatGlYv349tm3bhhYtWpjvDw4ORk1NDUpKSupsn5+fj+Dg4HpfS6VSQa1W17mRc2ob5I3lj/WBp6sLdp06jyf/m8ySQk7FYBSYufoQfj+aB1cXOZY80hN9W/lJHYvI4ixeUIQQmDZtGn7++Wds3boVkZGRdR7v2bMnlEoltmzZYr7v+PHjyMzMRGxsrKXjkAOKDvPB14/2hrvSBQknCvHMiv2ormVJIcdnNAq8/ONh/HooBwq5DP8Z3wMD2gdKHYvIKixeUKZOnYpvv/0WK1euhLe3N/Ly8pCXl4fKykoAgEajwaRJkzBz5kxs27YNycnJeOyxxxAbG3tTZ/AQAUBMKz989WgvuCnl2JpWgKkrDqCm1ih1LCKrEULgX2uP4ofkbLjIZVg0rjviOwZJHYvIaixeUD777DNotVoMGDAAISEh5tv3339v3uajjz7CPffcg1GjRqF///4IDg7GTz/9ZOko5ODiWvvjywm9oVLIsTk1H8+uOgC9gSWFHI8QAm+sS8GqPZmQyYAPH4jG0C68vg45Nquvg2INXAeFLpdwohCTl+9DjcGIYV1C8PHYblxB005xHZSrGY0Cr687hm8SzwIAPhjdFWN68UxGsk82tQ4KkbXd0S4ASx7pCaWLDBuO5OLZ73i4hxyD0Sjwz5+P4JvEs5DJgPfu78JyQk6DBYUcwp1RgfhsfE+4usjx25E8PPUtz+4h+2YwCvzfD4fw3d4syGXA/NHRGNsnXOpYRE2GBYUcRnzHIHwxsRdUCtPE2UnL96Kihou5kf3RG4yY8f1B/LT/HFzkMiwY2x2jera48ROJHAgLCjmUO9oFYPnjpnVS/ko/f/HqrnqpYxHdtJpaI6at3I91h3KgdJHh04d64N7oUKljETU5FhRyOH1b+eG/T8RA7abAvrMX8PCXvHYP2Yfy6lpMWr4XfxzLh6vCtAjbkM71L2BJ5OhYUMgh9QhvhlVT+sLX0xWHs7V48PNE5GorpY5FdE3F5TV46Msk7DhZBHelC76c0At3RXGdE3JeLCjksDqFavD9lL4IUqtwIr8Mo/6zCyfzS6WORXSVcyWVGLN4Fw5llcDHQ4mVk2PQv12A1LGIJMWCQg6tbZA3fnw6Dq0CPJGjrcLoxYlIPlssdSwis/SCUoz+bBdOFZYjROOGH56KRffwZlLHIpIcCwo5vBbNPPDjU3HoHu4DbaUeD32RhE0p+VLHIsL+zAsYvTgRudoqtA7wxA9Px6FNoLfUsYhsAgsKOYVmnq5Y8UQM7ooKRHWtEU/+dx++25MpdSxyYr8fycW4z3ejpEKP6DAfrHkqDs193KWORWQzWFDIaXi4KvD5Iz3xQK8WMArglZ+O4P2NaTAa7e5qD2THhBBYknAKT6/Yj+paI+5sH4CVT8TA19NV6mhENoUFhZyKwkWO90d1xbN3tQEAfPbnKTyzYj8XdKMmoTcY8c+fj2Du72kAgEfjWuKLCb3gqVJInIzI9rCgkNORyWSYOag9PnowGq4ucmw8locHliQiT1sldTRyYLoqPR5fther9piWrp8zvCNev7cTL2xJdA38ZJDTuq97C6ycHAM/T1ccPafDiE934ki2VupY5IAyisox6j+7sONkETxcXfD5I73wWL9IqWMR2TQWFHJqvVr6Yu3Ufmgb6IV8XTXGLNmFXw/lSB2L7JHRAOT/CZxZZfqv0XSxym1pBbj3k504WVCGILUKq5+MRXxHLsBGdCM88ElOL8zXAz8+E4fpKw8g4UQhnl11AIeySvDK0CgoOfxONyPrJyD5OaAi23yXcG+BjapX8MyOlhAC6BXRDP95uAcCvd0kDEpkP/ivLxEAtZsSXz/aG88MaA0A+GpnBh7+MgmFpdUSJyNbZzj7I8SO0RCXlRMAQEU2Bl+YhkHeuzA+JhwrJ/dlOSFqABYUootc5DK8NCQKix/uAU9XFyRlFGP4op3Yn3lB6mhkozYeyUZRwlMQQkB2xWOyi3d82GYp3hnREa4K/nNL1BD8xBBdYUjnEPwy7Ta0DvBEnq4KDy5JxLK/MiAE10uhv208motla/+LIEUR5Fe2k4vkMsCzNhco3NG04YgcAAsKUT3aBHrhl2m3YUinYOgNAq+vS8Hkb5JxobxG6mhkAwxGgTfWpSBQeZOja5W51g1E5IBYUIiuwUulwGcP98Cc4R3h6iLH5tR8DP14B5JOn5c6GklsT0YxcrVVKNDf5EX93EOsG4jIAbGgEF2HTCbDY/0i8dMzcWjlbzrkM+6L3Viw+QQMXCLfaRWUmhb121PeCTk1/rjWXwUBGeARBgTc3oTpiBwDCwrRTejcXIN102/D6J6m6/gs2HwSYxbvwunCMqmjkQRcLs6ANcIFb+RMMf3/FSXF/OueCwC5S9OFI3IQLChEN8lTpcD8MdFY8GA3eKkU2J9ZgrsX7sDSvzJ4wUEnIYTALwfP4Z8/HzHf94cuDk+f/Sfy9P51ti0wBMDYbw0Qdn9TxyRyCDJhh6cm6HQ6aDQaaLVaqNVqqeOQEzpXUomXfziMnelFAICYSF/MHxONMF8PiZPZN6PRiJSUFABAx44dIZfbzs9QhaXVeP3XY9hwxDThtaWfB86cr4AMgAAghwF9PI8hUHkBBfpmeHTkIxjSpYWkmYlsTUO+v23n009kR5r7uOO/k/rgrZGd4a40rZkyeMF2LN91hnNTHIwQAqv3ZSH+wwRsOJILF7kMz8e3w+aZd2Dxwz0QrDEtvmaEC3aXd8VeMRiP3vcoywnRLeIICtEtOnu+HC+uOYw9Z4oBANEtNHjnvi7o3FwjcTL7Y2sjKGfPl+OfPx/BX+mmM7c6harx/qiudfatwSiwJ6MYBaVVCPR2Q59IX7hca2EUIifXkO9vFhQiCzAaBb5NOosPNh5HaXUt5DLg8X6ReP4f7eCp4iWvbpatFJTqWgO+2pmBhVtOokpvhEohx8x/tMOk2yKh4PWZiBqtId/f/JeTyALkchkmxLbE4E7BeHN9CjYczsWXOzPw25FcvHpPRwztHAyZjD9V24NtaQV4c30KMorKAQBxrf3w7n1d0NLfU+JkRM6FBYXIgoLUbvj0oR4Y3bMAr609iuwLlXhmxX70ifTF7Hs68rCPDTtTVI4316dga1oBAMDfS4VZQ6Nwf4/mLJdEEuAhHiIrqawx4LOEU/h8+ylU6Y2QyYDRPVrgxcHtEajmVW3rI8UhnpKKGvznz1NY9tcZ1BiMULrI8Hi/SEy7qw283ZRW//2JnAnnoBDZkJySSszbmIa1B3MAAB6uLnjitkhMur0VNO78ArxcUxaUyhoDvv4rA4sTTqG0qhYA0L9dAOYM74jWAV5W+32JnBkLCpENOpB5AW+uT8GBzBIAgMZdiSn9W+HRuJacSHtRUxQUvcGI1fuy8PHmkygorQYARAV74+UhURjQPoCHc4isiAWFyEYJIfDHsXx8uOk4TuSblsn383TF0wNa46GYcHi4OndRsWZBqdIbsGZfFhYnnMa5kkoAQItm7nhhUDuMiG4OOU8NJrI6FhQiG2cwCqw/nIOPNp3AmfMVAAAfDyUmxrbExLiW8PV0lTihNKxRUMqra7EyKROf7ziNwosjJv5eKky901QKVQpeJ4eoqbCgENkJvcGIn/Zn4z9/nsLZi0XFTSnH2N7hmHRbpNMtnW/JgpKrrcR/E89i1Z5MXKjQAwBCNW54akBrPNArDG5KFhOipsaCQmRnDEaBjUfzsDjhFI6c0wIA5DLgrqggPNw3HP3bBjjFIYhbLShCCCSfvYClu85g49E882UHIvw88MyA1rivewu4KrjQGpFUuFAbkZ1xkcswrGsI7u4SjF2nzmNxwinsOFmEzan52Jyajwg/DzzUJxyje7aAn5dK6rg2p6SiBr8czMH3e7OQkqsz39+3lS8ejYvEPzoGcfl5IjvDERQiG3WqsAwrdmdiTXKW+TRYhVyGO9oF4L4ezRHfIcjhDlM0ZASl1mDEX6fOY/W+LGw6lo8agxEA4KqQY2S3UDwaF4mOofz3gciW8BAPkQOpqKnFukM5WJGUicPZWvP93ioFhnYJxtAuIYhr7ecQkz1vVFBqDUbsPl2MDUdy8b9jeThfXmN+rEOIGg/0aoER3Zo77SRjIlvHgkLkoNILSvHT/nP45WCO+VRZAPBSKXBH+wAM6hiEAe0D7XYBuPoKiq5Kj13pRdiWVohNqfkovqyU+HgoMSI6FGN6hfEyAkR2gAWFyMEZjQJ7zhRj3aEcbErJNy84Bpgm13Zt4YN+bfzQr7U/ekQ0s5tDQUajEYeOHMOp4hrkCjV2nChCcuYF82RXAPD1dMXgTkG4u0sI+rbyg5JXFyayGywoRE7EaBQ4fE6L/x3Lw/9S8pFeUFbncZVCjq4tNIhu4YPoMB90C/NBi2buNrFiqhAC+bpqHMvRIvnsBew7U4yDWSWoMdT9Z6lVgCfuaBeAgVFB6NvKFwqWEiK7xIJC5MTOlVRiV3oRdp06j53pRebFyS7XzEOJtkHeaBvohTaBXmgb6I0IPw8Eqd2schpurcGInJIqZBZXILO4AukFZUjN1SEtT2deo+RyapUcMa380b99IAa0C3C69WCIHJXdFJRPP/0UH3zwAfLy8hAdHY1FixahT58+N3weCwrRzRFC4FRhOQ5mleBQVgkOZZcgJUeHWmP9H3uZzLTKaqjGDcEaN/i4u0LtroDaTQm1uxLuShe4yGXmm1wmQ3WtAVV6o/m/uio9istqcL68GufLa1BUVo2ckqo6h2ku5yKXIdLfE93CfNAzwgc++mK0UCvQqVOnJrmaMRE1HbtYB+X777/HzJkzsXjxYsTExGDBggUYPHgwjh8/jsDAQKliETkUmUyGNhdHSUb3bAHAdE2ak/llSC8sNf23wHTLLqlETa0RhaXVKCytxqHLzhiyBFeFHOG+Hgj39UBLP09EhXijY4gabQK9zHNkTJNkSy36+xKRfZJsBCUmJga9e/fGJ598AsD0D1NYWBimT5+OV1555brPvdTALly4wBEUIgsRQuB8eQ3ytFXI1VYhT1cFXVUtSiv10FXVQlupR5XeAINRwCAEDAbTf1UKOdyULnBTuECllMNbpYCflyt8PV3h56WCr6crWvi4I9BbdcPVcI1GI9LS0gAAUVFRHEEhcjA6nQ7NmjWz3RGUmpoaJCcnY9asWeb75HI54uPjkZiYeNX21dXVqK7++zi6TmdaKTItLQ1eXl7WD0zkROQAmgNorgagvvzehqxgW3PxVgaUA8XlQHEDc1wqKkTkOMrKym680UWS/HhSVFQEg8GAoKCgOvcHBQUhLy/vqu3nzp0LjUZjvoWFhTVVVCIiIpKAXVyLZ9asWZg5c6b51zqdDmFhYYiKiuIhHiIHwkM8RI7t0hGQmyFJQfH394eLiwvy8/Pr3J+fn4/g4OCrtlepVFCprh5elsvl/AeMyEHx803keBrymZbk0+/q6oqePXtiy5Yt5vuMRiO2bNmC2NhYKSIRERGRDZHsEM/MmTMxceJE9OrVC3369MGCBQtQXl6Oxx57TKpIREREZCMkKygPPvggCgsLMXv2bOTl5aFbt27YuHHjVRNniYiIyPlwqXsishn1Xc2YiBxHQ76/+eknIiIim8OCQkRERDaHBYWIiIhsDgsKERER2RwWFCIiIrI5LChERERkc1hQiIiIyOawoBAREZHNYUEhIiIimyPZUve34tLitw25bDMR2T6j0YiysjIAps83V5IlciyXvrdvZhF7uywopaWlAICwsDCJkxAREVFDlZaWQqPRXHcbu7wWj9FoRE5ODry9vSGTySz62jqdDmFhYcjKynLI6/zw/dk/R3+PfH/2z9Hfo6O/P8B671EIgdLSUoSGht5whNQuR1DkcjlatGhh1d9DrVY77F88gO/PETj6e+T7s3+O/h4d/f0B1nmPNxo5uYQHeImIiMjmsKAQERGRzWFBuYJKpcKcOXOgUqmkjmIVfH/2z9HfI9+f/XP09+jo7w+wjfdol5NkiYiIyLFxBIWIiIhsDgsKERER2RwWFCIiIrI5LChERERkc5yuoLzzzjuIi4uDh4cHfHx86t0mMzMTw4YNg4eHBwIDA/Hiiy+itrb2uq9bXFyM8ePHQ61Ww8fHB5MmTTJfU0RKf/75J2QyWb23vXv3XvN5AwYMuGr7p556qgmT37yWLVtelfW999677nOqqqowdepU+Pn5wcvLC6NGjUJ+fn4TJb55Z86cwaRJkxAZGQl3d3e0bt0ac+bMQU1NzXWfZ+v779NPP0XLli3h5uaGmJgY7Nmz57rbr1mzBlFRUXBzc0OXLl3w22+/NVHShps7dy569+4Nb29vBAYGYuTIkTh+/Ph1n7Ns2bKr9pebm1sTJW6Y119//aqsUVFR132OPe2/+v49kclkmDp1ar3b28O+2759O4YPH47Q0FDIZDKsXbu2zuNCCMyePRshISFwd3dHfHw8Tp48ecPXbejnuKGcrqDU1NRgzJgxePrpp+t93GAwYNiwYaipqcGuXbuwfPlyLFu2DLNnz77u644fPx7Hjh3Dpk2bsH79emzfvh1TpkyxxltokLi4OOTm5ta5PfHEE4iMjESvXr2u+9zJkyfXed68efOaKHXDvfnmm3WyTp8+/brbP//881i3bh3WrFmDhIQE5OTk4P7772+itDcvLS0NRqMRS5YswbFjx/DRRx9h8eLF+Oc//3nD59rq/vv+++8xc+ZMzJkzB/v370d0dDQGDx6MgoKCerfftWsXxo0bh0mTJuHAgQMYOXIkRo4ciaNHjzZx8puTkJCAqVOnYvfu3di0aRP0ej0GDRqE8vLy6z5PrVbX2V9nz55tosQN16lTpzpZd+7cec1t7W3/7d27t85727RpEwBgzJgx13yOre+78vJyREdH49NPP6338Xnz5mHhwoVYvHgxkpKS4OnpicGDB6Oqquqar9nQz3GjCCe1dOlSodForrr/t99+E3K5XOTl5Znv++yzz4RarRbV1dX1vlZKSooAIPbu3Wu+7/fffxcymUycO3fO4tlvRU1NjQgICBBvvvnmdbe74447xHPPPdc0oW5RRESE+Oijj256+5KSEqFUKsWaNWvM96WmpgoAIjEx0QoJLWvevHkiMjLyutvY8v7r06ePmDp1qvnXBoNBhIaGirlz59a7/QMPPCCGDRtW576YmBjx5JNPWjWnpRQUFAgAIiEh4ZrbXOvfI1s0Z84cER0dfdPb2/v+e+6550Tr1q2F0Wis93F72ndCCAFA/Pzzz+ZfG41GERwcLD744APzfSUlJUKlUolVq1Zd83Ua+jluDKcbQbmRxMREdOnSBUFBQeb7Bg8eDJ1Oh2PHjl3zOT4+PnVGJOLj4yGXy5GUlGT1zA3x66+/4vz583jsscduuO2KFSvg7++Pzp07Y9asWaioqGiChI3z3nvvwc/PD927d8cHH3xw3UNyycnJ0Ov1iI+PN98XFRWF8PBwJCYmNkXcW6LVauHr63vD7Wxx/9XU1CA5ObnOn71cLkd8fPw1/+wTExPrbA+YPpP2sK8A0/4CcMN9VlZWhoiICISFhWHEiBHX/PfGFpw8eRKhoaFo1aoVxo8fj8zMzGtua8/7r6amBt9++y0ef/zx616Y1p723ZUyMjKQl5dXZx9pNBrExMRccx815nPcGHZ5sUBrysvLq1NOAJh/nZeXd83nBAYG1rlPoVDA19f3ms+RyldffYXBgwff8GKLDz30ECIiIhAaGorDhw/j5ZdfxvHjx/HTTz81UdKb9+yzz6JHjx7w9fXFrl27MGvWLOTm5uLDDz+sd/u8vDy4urpeNQcpKCjI5vbXldLT07Fo0SLMnz//utvZ6v4rKiqCwWCo9zOWlpZW73Ou9Zm09X0FmK68PmPGDPTr1w+dO3e+5nbt27fH119/ja5du0Kr1WL+/PmIi4vDsWPHrH5h1IaKiYnBsmXL0L59e+Tm5uKNN97A7bffjqNHj8Lb2/uq7e15/61duxYlJSV49NFHr7mNPe27+lzaDw3ZR435HDeGQxSUV155Be+///51t0lNTb3hRC570pj3nJ2djT/++AOrV6++4etfPn+mS5cuCAkJwcCBA3Hq1Cm0bt268cFvUkPe38yZM833de3aFa6urnjyyScxd+5cm12KujH779y5cxgyZAjGjBmDyZMnX/e5Uu8/Mpk6dSqOHj163TkaABAbG4vY2Fjzr+Pi4tChQwcsWbIEb731lrVjNsjQoUPN/9+1a1fExMQgIiICq1evxqRJkyRMZnlfffUVhg4ditDQ0GtuY0/7zt44REF54YUXrttwAaBVq1Y39VrBwcFXzUS+dHZHcHDwNZ9z5cSg2tpaFBcXX/M5t6ox73np0qXw8/PDvffe2+DfLyYmBoDpJ/im+IK7lX0aExOD2tpanDlzBu3bt7/q8eDgYNTU1KCkpKTOKEp+fr7V9teVGvr+cnJycOeddyIuLg6ff/55g3+/pt5/1+Lv7w8XF5erzpi63p99cHBwg7a3FdOmTTNPmG/oT9JKpRLdu3dHenq6ldJZjo+PD9q1a3fNrPa6/86ePYvNmzc3eNTRnvYd8Pf3Wn5+PkJCQsz35+fno1u3bvU+pzGf40ax2GwWO3OjSbL5+fnm+5YsWSLUarWoqqqq97UuTZLdt2+f+b4//vjDpibJGo1GERkZKV544YVGPX/nzp0CgDh06JCFk1net99+K+RyuSguLq738UuTZH/44QfzfWlpaTY7STY7O1u0bdtWjB07VtTW1jbqNWxp//Xp00dMmzbN/GuDwSCaN29+3Umy99xzT537YmNjbXaSpdFoFFOnThWhoaHixIkTjXqN2tpa0b59e/H8889bOJ3llZaWimbNmomPP/643sftbf9dMmfOHBEcHCz0en2Dnmfr+w7XmCQ7f/58831arfamJsk25HPcqKwWeyU7cfbsWXHgwAHxxhtvCC8vL3HgwAFx4MABUVpaKoQw/eXq3LmzGDRokDh48KDYuHGjCAgIELNmzTK/RlJSkmjfvr3Izs423zdkyBDRvXt3kZSUJHbu3Cnatm0rxo0b1+Tv71o2b94sAIjU1NSrHsvOzhbt27cXSUlJQggh0tPTxZtvvin27dsnMjIyxC+//CJatWol+vfv39Sxb2jXrl3io48+EgcPHhSnTp0S3377rQgICBATJkwwb3Pl+xNCiKeeekqEh4eLrVu3in379onY2FgRGxsrxVu4ruzsbNGmTRsxcOBAkZ2dLXJzc823y7exp/333XffCZVKJZYtWyZSUlLElClThI+Pj/nMuUceeUS88sor5u3/+usvoVAoxPz580VqaqqYM2eOUCqV4siRI1K9het6+umnhUajEX/++Wed/VVRUWHe5sr3+MYbb4g//vhDnDp1SiQnJ4uxY8cKNzc3cezYMSnewnW98MIL4s8//xQZGRnir7/+EvHx8cLf318UFBQIIex//wlh+rINDw8XL7/88lWP2eO+Ky0tNX/XARAffvihOHDggDh79qwQQoj33ntP+Pj4iF9++UUcPnxYjBgxQkRGRorKykrza9x1111i0aJF5l/f6HNsCU5XUCZOnCgAXHXbtm2beZszZ86IoUOHCnd3d+Hv7y9eeOGFOi1627ZtAoDIyMgw33f+/Hkxbtw44eXlJdRqtXjsscfMpccWjBs3TsTFxdX7WEZGRp0/g8zMTNG/f3/h6+srVCqVaNOmjXjxxReFVqttwsQ3Jzk5WcTExAiNRiPc3NxEhw4dxLvvvltntOvK9yeEEJWVleKZZ54RzZo1Ex4eHuK+++6r86VvK5YuXVrv39fLBz/tcf8tWrRIhIeHC1dXV9GnTx+xe/du82N33HGHmDhxYp3tV69eLdq1aydcXV1Fp06dxIYNG5o48c271v5aunSpeZsr3+OMGTPMfx5BQUHi7rvvFvv372/68DfhwQcfFCEhIcLV1VU0b95cPPjggyI9Pd38uL3vPyFMI+AAxPHjx696zB733aXvrCtvl96H0WgUr732mggKChIqlUoMHDjwqvceEREh5syZU+e+632OLUEmhBCWO2BEREREdOu4DgoRERHZHBYUIiIisjksKERERGRzWFCIiIjI5rCgEBERkc1hQSEiIiKbw4JCRERENocFhYiIiGwOCwoRERHZHBYUIiIisjksKERERGRzWFCIiIjI5vw/5vxxpVFq7VwAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(2, 10)\n", "plt.scatter(2.1, 10.61, c = 'orange', zorder=3)\n", "_ = ax.plot(x,y)"]}, {"cell_type": "code", "execution_count": 150, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YlWhoumBcxaY", "outputId": "5edba389-0ca7-4d46-dd51-076c0a3e2622"}, "outputs": [{"data": {"text/plain": ["6.099999999999989"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["m = (10.61-10)/(2.1-2)\n", "m"]}, {"cell_type": "code", "execution_count": 151, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KHMZNdjQcxaa", "outputId": "a49f1b35-05c4-402f-fcf2-bb666a3c5a9c"}, "outputs": [{"data": {"text/plain": ["-2.199999999999978"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["b = 10.61-m*2.1\n", "b"]}, {"cell_type": "code", "execution_count": 152, "metadata": {"id": "J1-_0DBJcxab"}, "outputs": [], "source": ["line_y = m*x + b"]}, {"cell_type": "code", "execution_count": 153, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "eDtjdRYQcxad", "outputId": "aab8a88c-df28-4d2e-e5f9-79bcf70942c9"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(2, 10)\n", "plt.scatter(2.1, 10.61, c='orange', zorder=3)\n", "plt.ylim(-5, 150)\n", "plt.plot(x, line_y, c='orange', zorder=3)\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "elxPRnoTcxae"}, "source": ["The closer $Q$ becomes to $P$ (i.e., $\\Delta x$ approaches 0), the clearer it becomes that the slope $m$ at point $P$ = (2, 10) is equal to 6."]}, {"cell_type": "markdown", "metadata": {"id": "srMjU62xcxaf"}, "source": ["Let's make $\\Delta x$ extremely small, 0.000001, to illustrate this:"]}, {"cell_type": "code", "execution_count": 154, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "c_B0wY47cxaf", "outputId": "88d748a1-8ae1-4913-f995-82c03f61ab87"}, "outputs": [{"data": {"text/plain": ["1e-06"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["delta_x = 0.000001\n", "delta_x"]}, {"cell_type": "code", "execution_count": 155, "metadata": {"id": "g8LSgemacxai"}, "outputs": [], "source": ["x1 = 2\n", "y1 = 10"]}, {"cell_type": "markdown", "metadata": {"id": "gsa2I2Jxcxaj"}, "source": ["Rearranging $\\Delta x = x_2 - x_1$, we can calculate $x_2$ for our point $Q$, which is now extremely close to $P$: \n", "$$x_2 = x_1 + \\Delta x$$"]}, {"cell_type": "code", "execution_count": 156, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4PgqVCZVcxak", "outputId": "9efbfa02-c14b-416a-da25-d7e07c44e1ed"}, "outputs": [{"data": {"text/plain": ["2.000001"]}, "execution_count": 156, "metadata": {}, "output_type": "execute_result"}], "source": ["x2 = x1 + delta_x\n", "x2"]}, {"cell_type": "markdown", "metadata": {"id": "FoBqhu1zcxan"}, "source": ["$y_2$ for our point $Q$ can be obtained with the usual function $f(x)$: \n", "$$y_2 = f(x_2)$$"]}, {"cell_type": "code", "execution_count": 157, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9_2mjm1Ycxan", "outputId": "8d908d59-7e94-4648-8867-9950ea7b3661"}, "outputs": [{"data": {"text/plain": ["10.000006000001001"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["y2 = f(x2)\n", "y2"]}, {"cell_type": "markdown", "metadata": {"id": "Eekls7AIcxap"}, "source": ["To find the slope $m$, we continue to use $$m = \\frac{\\Delta y}{\\Delta x} = \\frac{y_2 - y_1}{x_2 - x_1}$$"]}, {"cell_type": "code", "execution_count": 158, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yQAwKXPUcxap", "outputId": "c07ba5e1-2cec-489f-aa71-3a40db79f0b7"}, "outputs": [{"data": {"text/plain": ["6.000001000088901"]}, "execution_count": 158, "metadata": {}, "output_type": "execute_result"}], "source": ["m = (y2 - y1)/(x2 - x1)\n", "m"]}, {"cell_type": "markdown", "metadata": {"id": "IEy7jMVpcxar"}, "source": ["Boom! Using the delta method, we've shown that at point $P$, the slope of the curve is 6. "]}, {"cell_type": "markdown", "metadata": {"id": "GJ7bRkBvcxar"}, "source": ["**Exercise**: Using the delta method, find the slope of the tangent where $x = -1$."]}, {"cell_type": "markdown", "metadata": {"id": "pV8Vpgvdcxar"}, "source": ["**Spoiler alert! The solution's below.**"]}, {"cell_type": "code", "execution_count": 159, "metadata": {"id": "65FeJSRocxaz"}, "outputs": [], "source": ["x1 = -1"]}, {"cell_type": "code", "execution_count": 160, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fAIGLk8Mcxa0", "outputId": "85a2864a-dc7a-45a1-fce9-1fc164ab90fe"}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}], "source": ["y1 = f(x1)\n", "y1"]}, {"cell_type": "markdown", "metadata": {"id": "SZowMdIQcxa1"}, "source": ["Point $P$ is located at (-1, 1)"]}, {"cell_type": "code", "execution_count": 161, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-CPHl783cxa1", "outputId": "e74a4347-652e-4e40-a7e1-83f87a51f776"}, "outputs": [{"data": {"text/plain": ["1e-06"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["delta_x"]}, {"cell_type": "code", "execution_count": 162, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jzw0J1n9cxa2", "outputId": "53c6056e-caec-4c33-fe0b-8369ec1cc0bd"}, "outputs": [{"data": {"text/plain": ["-0.999999"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["x2 = x1 + delta_x\n", "x2"]}, {"cell_type": "code", "execution_count": 163, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WaynVKiwcxa3", "outputId": "f5c682ad-93bb-49b2-f171-952f521e45c9"}, "outputs": [{"data": {"text/plain": ["1.000000000001"]}, "execution_count": 163, "metadata": {}, "output_type": "execute_result"}], "source": ["y2 = f(x2)\n", "y2"]}, {"cell_type": "markdown", "metadata": {"id": "rApig2ipcxa4"}, "source": ["Quick aside: Pertinent to defining differentiation as an equation, an alternative way to calculate $y_2$ is $f(x + \\Delta x)$"]}, {"cell_type": "code", "execution_count": 164, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yCW283sZcxa4", "outputId": "87008c5f-0184-4262-edc4-b571d533f2c4"}, "outputs": [{"data": {"text/plain": ["1.000000000001"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["y2 = f(x1 + delta_x)\n", "y2"]}, {"cell_type": "markdown", "metadata": {"id": "S1Dr3Ankcxa7"}, "source": ["Point $Q$ is at (-0.999999, 1.000000000001), extremely close to $P$."]}, {"cell_type": "code", "execution_count": 165, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZtokXlMncxa7", "outputId": "676f0135-60cc-44b1-f1c0-adaf6def3395"}, "outputs": [{"data": {"text/plain": ["1.0000889005535828e-06"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["m = (y2-y1)/(x2-x1)\n", "m"]}, {"cell_type": "markdown", "metadata": {"id": "91slg8F4cxa8"}, "source": ["Therefore, as $x_2$ becomes infinitely close to $x_1$, it becomes clear that the slope $m$ at $x_1 = -1$ is equal to zero. Let's plot it out: "]}, {"cell_type": "code", "execution_count": 166, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2WToJBOhcxa8", "outputId": "722eab24-e3da-4180-9e51-ac003be91f41"}, "outputs": [{"data": {"text/plain": ["1.0000010000889006"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}], "source": ["b = y2-m*x2\n", "b"]}, {"cell_type": "code", "execution_count": 167, "metadata": {"id": "ah0cXRhrcxa9"}, "outputs": [], "source": ["line_y = m*x + b"]}, {"cell_type": "code", "execution_count": 168, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 265}, "id": "MjLttDg9cxa-", "outputId": "e3827009-0d8f-46a1-81cd-c2425c2b308b"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "plt.axvline(x=0, color='lightgray')\n", "plt.axhline(y=0, color='lightgray')\n", "plt.scatter(x1, y1)\n", "plt.scatter(x2, y2, c='orange', zorder=3)\n", "plt.ylim(-5, 150)\n", "plt.plot(x, line_y, c='orange', zorder=3)\n", "_ = ax.plot(x,y)"]}, {"cell_type": "markdown", "metadata": {"id": "hqmMtmpGcxbA"}, "source": ["As $Q$ becomes infinitely close to $P$:\n", "* $x_2$ - $x_1$ approaches 0\n", "* In other words, $\\Delta x$ approaches 0\n", "* This can be denoted as $\\Delta x \\to 0$"]}, {"cell_type": "markdown", "metadata": {"id": "nsh_7o7kcxbA"}, "source": ["Using the delta method, we've derived the definition of differentiation from first principles. The derivative of $y$ (denoted $dy$) with respect to $x$ (denoted $dx$) can be represented as: \n", "$$\\frac{dy}{dx} = \\lim_{\\Delta x \\to 0} \\frac{\\Delta y}{\\Delta x}$$"]}, {"cell_type": "markdown", "metadata": {"id": "UxuWTEKlcxbA"}, "source": ["Expanding $\\Delta y$ out to $y_2 - y_1$: \n", "$$\\frac{dy}{dx} = \\lim_{\\Delta x \\to 0} \\frac{y_2 - y_1}{\\Delta x}$$"]}, {"cell_type": "markdown", "metadata": {"id": "b0H4kFkYcxbB"}, "source": ["Finally, replacing $y_1$ with $f(x)$ and replacing $y_2$ with $f(x + \\Delta x)$, we obtain a common representation of differentiation:\n", "$$\\frac{dy}{dx} = \\lim_{\\Delta x \\to 0} \\frac{f(x + \\Delta x) - f(x)}{\\Delta x}$$"]}, {"cell_type": "markdown", "metadata": {"id": "SuYWQ9e1cxbB"}, "source": ["Let's observe the differentiation equation in action: "]}, {"cell_type": "code", "execution_count": 169, "metadata": {"id": "Do6EHZfHcxbB"}, "outputs": [], "source": ["def diff_demo(my_f, my_x, my_delta):\n", "    return (my_f(my_x + my_delta) - my_f(my_x)) / my_delta"]}, {"cell_type": "code", "execution_count": 170, "metadata": {"id": "hXbAgWvrcxbC"}, "outputs": [], "source": ["deltas = [1, 0.1, 0.01, 0.001, 0.0001, 0.00001, 0.000001]"]}, {"cell_type": "code", "execution_count": 171, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iYtlA3S5cxbD", "outputId": "ea82a37a-e15a-4343-ccd1-73e459070f08"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.0\n", "6.099999999999994\n", "6.009999999999849\n", "6.000999999999479\n", "6.000100000012054\n", "6.000009999951316\n", "6.000001000927568\n"]}], "source": ["for delta in deltas:\n", "    print(diff_demo(f, 2, delta))"]}, {"cell_type": "code", "execution_count": 172, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CMW45DQmcxbF", "outputId": "307410ee-4154-4b91-f9a3-ff6263557a38", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "0.10000000000000009\n", "0.009999999999998899\n", "0.001000000000139778\n", "9.99999993922529e-05\n", "1.000000082740371e-05\n", "1.000088900582341e-06\n"]}], "source": ["for delta in deltas:\n", "    print(diff_demo(f, -1, delta))"]}, {"cell_type": "markdown", "metadata": {"id": "Kh8vawMMcxbG"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "Ez8pKj_lcxbG"}, "source": ["## Segment 3: Automatic Differentiation"]}, {"cell_type": "markdown", "metadata": {"id": "laWM6cgvcxbG"}, "source": ["**TensorFlow** and **PyTorch** are the two most popular automatic differentiation libraries."]}, {"cell_type": "markdown", "metadata": {"id": "RLi7jwLEzaDB"}, "source": ["Let's use them to calculate $dy/dx$ at $x = 5$ where: "]}, {"cell_type": "markdown", "metadata": {"id": "qmgeCDWycxbL"}, "source": ["$$y = x^2$$"]}, {"cell_type": "markdown", "metadata": {"id": "MhMxT9oQcxbL"}, "source": ["$$ \\frac{dy}{dx} = 2x = 2(5) = 10 $$"]}, {"cell_type": "markdown", "metadata": {"id": "Zg-PepdncxbN"}, "source": ["### Autodiff with <PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 173, "metadata": {"id": "n8FJ1OkWcxbN"}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'torch'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[173], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'torch'"]}], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"id": "DTYVufujcxbP"}, "outputs": [], "source": ["x = torch.tensor(5.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "13j9vFZgcxbP", "outputId": "982fb7b9-cd4a-4d6d-dcfb-397626d30ca5"}, "outputs": [], "source": ["x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PBLvpsCWcxbQ", "outputId": "6f50d93d-6e9d-40b2-f66a-2190b2b1e445"}, "outputs": [], "source": ["x.requires_grad_() # contagiously track gradients through forward pass"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"id": "zper8WoVcxbR"}, "outputs": [], "source": ["y = x**2"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"id": "1Pbdg7zVcxbS"}, "outputs": [], "source": ["y.backward() # use autodiff"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UbV3BecacxbT", "outputId": "9d3f5270-a22c-40cc-dede-e51d75209c6a"}, "outputs": [], "source": ["x.grad"]}, {"cell_type": "markdown", "metadata": {"id": "dyB3FghkcxbG"}, "source": ["### Autodiff with TensorFlow"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"id": "4D12um9kcxbH"}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"id": "f7mQuelZcxbJ"}, "outputs": [], "source": ["x = tf.Variable(5.0)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"id": "M-WeFuBfcxbK"}, "outputs": [], "source": ["with tf.<PERSON><PERSON><PERSON><PERSON>() as t:\n", "    t.watch(x) # track forward pass\n", "    y = x**2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Txjp5NBNcxbL", "outputId": "00862329-4eae-4129-f6e7-971c718039f5"}, "outputs": [], "source": ["t.gradient(y, x) # use autodiff"]}, {"cell_type": "markdown", "metadata": {"id": "dS--vwVWzaDD"}, "source": ["**Return to slides here.**"]}, {"cell_type": "markdown", "metadata": {"id": "l599s7vacxbV"}, "source": ["As usual, PyTorch feels more intuitive and pythonic than TensorFlow. See the standalone [*Regression in PyTorch*](https://github.com/jonkrohn/ML-foundations/blob/master/notebooks/regression-in-pytorch.ipynb) notebook for an example of autodiff paired with gradient descent in order to fit a simple regression line."]}], "metadata": {"colab": {"include_colab_link": true, "name": "3-calculus-i.ipynb", "provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 0}