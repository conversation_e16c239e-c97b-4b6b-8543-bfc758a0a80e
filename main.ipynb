import pandas as pd
import numpy as np
import os

data = np.array([[1, 4], [2, 5], [3, 6]])

df = pd.DataFrame(data, index=['row1','row2','row3'], columns=['col1', 'col2'])
df

states = ['california', 'texas', 'florida', 'new york']
population = [39250017, 29730311, 21477737, 19651127]

dict_states = {'States': states, 'Population': population}

df_population = pd.DataFrame(dict_states, index=[1,2,3,4])
df_population

# reading the csv file
df_passengers = pd.read_csv('titanic.csv')


df_passengers.head()

import random

new_index = np.arange(0, 891)
random.shuffle(new_index)

df_passengers['new_index'] = new_index

df_passengers.head()

