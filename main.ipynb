import pandas as pd
import numpy as np
import os

data = np.array([[1, 4], [2, 5], [3, 6]])

df = pd.DataFrame(data, index=['row1','row2','row3'], columns=['col1', 'col2'])
df

states = ['california', 'texas', 'florida', 'new york']
population = [39250017, 29730311, 21477737, 19651127]

dict_states = {'States': states, 'Population': population}

df_population = pd.DataFrame(dict_states, index=[1,2,3,4])
df_population

# reading the csv file
df_passengers = pd.read_csv('titanic.csv')


df_passengers.head()

import random

new_index = np.arange(0, 891)
random.shuffle(new_index)

df_passengers['new_index'] = new_index

df_passengers.head()

# Create array from 2 to 9 (10 is excluded)
arr2 = np.arange(2, 10)
print(arr2)  # Output: [2 3 4 5 6 7 8 9]

# Create integer array explicitly
arr7 = np.arange(5, dtype=int)
print(arr7)  # Output: [0 1 2 3 4]

# Create float array explicitly
arr8 = np.arange(5, dtype=float)
print(arr8)  # Output: [0. 1. 2. 3. 4.]

# Create complex array
arr9 = np.arange(3, dtype=complex)
print(arr9)  # Output: [0.+0.j 1.+0.j 2.+0.j]

