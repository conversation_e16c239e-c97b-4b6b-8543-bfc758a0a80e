{"cells": [{"cell_type": "code", "execution_count": 34, "id": "a3ab408d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": 35, "id": "7c117704", "metadata": {}, "outputs": [], "source": ["data = np.array([[1, 4], [2, 5], [3, 6]])"]}, {"cell_type": "code", "execution_count": 36, "id": "31b1f5b3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col1</th>\n", "      <th>col2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>row1</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row2</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>row3</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      col1  col2\n", "row1     1     4\n", "row2     2     5\n", "row3     3     6"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(data, index=['row1','row2','row3'], columns=['col1', 'col2'])\n", "df"]}, {"cell_type": "code", "execution_count": 37, "id": "4d018138", "metadata": {}, "outputs": [], "source": ["states = ['california', 'texas', 'florida', 'new york']\n", "population = [39250017, 29730311, 21477737, 19651127]"]}, {"cell_type": "code", "execution_count": 38, "id": "5968d706", "metadata": {}, "outputs": [], "source": ["dict_states = {'States': states, 'Population': population}"]}, {"cell_type": "code", "execution_count": 39, "id": "f7f8c9ee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>States</th>\n", "      <th>Population</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>california</td>\n", "      <td>39250017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>texas</td>\n", "      <td>29730311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>florida</td>\n", "      <td>21477737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>new york</td>\n", "      <td>19651127</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       States  Population\n", "1  california    39250017\n", "2       texas    29730311\n", "3     florida    21477737\n", "4    new york    19651127"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df_population = pd.DataFrame(dict_states, index=[1,2,3,4])\n", "df_population"]}, {"cell_type": "code", "execution_count": 40, "id": "56a2b633", "metadata": {}, "outputs": [], "source": ["# reading the csv file\n", "df_passengers = pd.read_csv('titanic.csv')\n"]}, {"cell_type": "code", "execution_count": 41, "id": "06196073", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PassengerId  Survived  Pclass  \\\n", "0            1         0       3   \n", "1            2         1       1   \n", "2            3         1       3   \n", "3            4         1       1   \n", "4            5         0       3   \n", "\n", "                                                Name     Sex   Age  SibSp  \\\n", "0                            <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1  C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "3       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  35.0      1   \n", "4                           <PERSON>, Mr. <PERSON>    male  35.0      0   \n", "\n", "   Parch            Ticket     Fare Cabin Embarked  \n", "0      0         A/5 21171   7.2500   NaN        S  \n", "1      0          PC 17599  71.2833   C85        C  \n", "2      0  STON/O2. 3101282   7.9250   NaN        S  \n", "3      0            113803  53.1000  C123        S  \n", "4      0            373450   8.0500   NaN        S  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df_passengers.head()"]}, {"cell_type": "code", "execution_count": 42, "id": "416a31af", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "new_index = np.arange(0, 891)\n", "random.shuffle(new_index)"]}, {"cell_type": "code", "execution_count": 43, "id": "a7b986df", "metadata": {}, "outputs": [], "source": ["df_passengers['new_index'] = new_index"]}, {"cell_type": "code", "execution_count": 44, "id": "3a6c6f3f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "      <th>new_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "      <td>341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>851</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "      <td>268</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>337</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PassengerId  Survived  Pclass  \\\n", "0            1         0       3   \n", "1            2         1       1   \n", "2            3         1       3   \n", "3            4         1       1   \n", "4            5         0       3   \n", "\n", "                                                Name     Sex   Age  SibSp  \\\n", "0                            <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1  C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "3       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  35.0      1   \n", "4                           <PERSON>, Mr. <PERSON>    male  35.0      0   \n", "\n", "   Parch            Ticket     Fare Cabin Embarked  new_index  \n", "0      0         A/5 21171   7.2500   NaN        S        688  \n", "1      0          PC 17599  71.2833   C85        C        341  \n", "2      0  STON/O2. 3101282   7.9250   NaN        S        851  \n", "3      0            113803  53.1000  C123        S        268  \n", "4      0            373450   8.0500   NaN        S        337  "]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_passengers.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ff6dd369", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}