import pandas as pd
import matplotlib.pyplot as plt

results = pd.read_csv('results.csv')

results.head()

results.loc[[1, 2, 3], ["event", "discipline", "type"]]

results.loc[1:3, ["event", "discipline", "type"]]# uses name of rows, columns

results.iloc[:3, [0,4]] # uses index values 

results.loc[1, "as"] = "Jeetheshwar" # we can change the values 

results.head()

results.at[1, "as"] # to get the value

results.iat[1, 4] #to get the value by index

results.type.head() # to get the column(if it is a single word)

results.sort_values(["year", "noc"], ascending=[0,1]).head() # to sort the values

for index, row in results.iterrows():
    print(index, row["event"]) # to iterate the values

results.loc[results["year"]>1890].head(3) # to get the values based on condition

results.loc[results["year"].between(1890, 1900)].head(3) # to get the values based on condition
results.loc[results["year"] > 1900, ["year", "event"]].head(3) # to get the values based on condition

results[results['as'].str.contains('J', case=False)] # to get the values based on starting letter

results[results['as'].str.contains('J|W', case=False)]

results[results["noc"].isin(["USA", "IND"])].head(5) 

results[results["noc"].isin(["USA", "IND"]) & results['as'].str.startswith("N")].head(4)

results.query("noc == 'IND'").head(3)

results['alive'] = True

results.head(3)

import numpy as np

results['alive'] = np.where(results['year'] > 1900, True, False)

results.head(3)

results.drop(0).head(3) # to drop the row

results = results.rename(columns={'as':'athlete', 'noc':'country'}) # to raname the column

results.head(3)

results_new = results.copy()

results_new['first_name'] = results_new['athlete'].str.split(' ').str.get(0) # adds new column

results_new.head(3)

results_new.query("first_name == 'Jeetheshwar'").head(3)

results_new['born_year'] = pd.to_datetime(results_new['year'])

results_new.head(3)

results_new.to_csv('results_new.csv', index=False) # to save the file

results_new['alive_now'] = results_new['alive'].apply(lambda x: 'Yes' if x else 'No')

results_new.head(3)

def categorize_year(row):
  if row['year'] < 1900:
    return '19th century'
  elif row['year'] < 2000:
    return '20th century'
  else:
    return '21st century'
  
  results_new['century'] = results_new.apply(categorize_year, axis=1) # to add new column based on condition

results_new.head(3)

results_new.drop(columns=['athlete_id'])

results_new.head(3)

bios = pd.read_csv('bios.csv')

bios.head(3)

nocs = pd.read_csv('athlete_events.csv')

nocs.head()

print(bios.columns)  # Check if 'born_region' exists in bios
print(nocs.columns)  # Check if 'NOC' exists in nocs


print(bios['born_region'].dtype)
print(nocs['NOC'].dtype)


print(bios['born_region'].unique()) # to get all the unique values
print(nocs['NOC'].unique())


if 'born_region' not in bios.columns:
    print("'born_region' column is missing in bios")
if 'NOC' not in nocs.columns:
    print("'NOC' column is missing in nocs")


bios_new = pd.merge(bios, nocs, left_on='born_region', right_on='NOC', how='left')


bios_new.rename(columns={'bith_date':'bd'}, inplace=True)

bios_new.head(3)

usa = bios_new[bios_new['born_country'] == 'USA'].copy()
gbr = bios_new[bios_new['born_country'] == 'GBR'].copy()

usa.head(3)

new_df = pd.concat([usa, gbr])

new_df

coffee = pd.read_csv('coffee.csv')

coffee.loc[[2, 3], 'Units Sold'] = np.nan #to change the values

coffee.head(3)

coffee.fillna(0).head() # to fill the NAN values, you can fill whatever the value you want

coffee.fillna(coffee['Units Sold'].mean()).head() # to fill the NAN values with mean

coffee.loc[[2, 3], 'Units Sold'] = np.nan

coffee.head(5)

coffee.dropna().head() # to drop the NAN values

coffee.dropna(subset=['Units Sold']).head() # to drop the NAN values based on column

bios['born_city'].value_counts()

bios[bios['born_country']=='USA']['born_region'].value_counts() # to get the number of people coming from each region from USA

coffee.groupby(['Coffee Type'])['Units Sold'].sum() # to get the sum of units sold based on coffee type

coffee.groupby(['Coffee Type'])['Units Sold'].agg(['sum', 'mean', 'std']) # to get the sum, mean, std of units sold based on coffee type

coffee.groupby(['Coffee Type']).agg({'Units Sold':'sum'})

coffee.head()

coffee.head(5)

pivot = coffee.pivot(columns='Coffee Type', index='Day', values='Revenue')

pivot

pivot.sum() # to get the sum of each column

coffee['yesterday_revenue'] = coffee['Revenue'].shift(2)

coffee.head(5)

