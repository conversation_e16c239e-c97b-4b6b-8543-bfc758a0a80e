{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import sys"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3]\n"]}], "source": ["a = np.array([1,2,3])\n", "print(a)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 3]\n", " [4 5 6]]\n"]}], "source": ["b = np.array([[1,2,3],[4,5,6]])\n", "print(b)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2)"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get Dimension\n", "a.ndim, b.ndim"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["(3,)"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get Shape\n", "a.shape\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2, 3)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["b.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('int64')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get Type \n", "a.dtype"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get Size\n", "a.itemsize"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["24"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get total size\n", "a.size * a.itemsize"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["24"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a.nbytes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["ACCESSING/CHANGING SPECIFIC ELEMENTS, ROWS, COLUMNS, ETC."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["a = np.array([[1,2,3,4,5,6,7],[8,9,10,11,12,13,14]])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13\n"]}], "source": ["# Get a specific element [r, c]\n", "print(a[1, 5]) # or -2\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5 6 7]\n"]}], "source": ["# Get a specific row\n", "print(a[0, :])\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 3, 10])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get a specific column\n", "a[:, 2]\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2 4 6]\n"]}], "source": ["# Getting a little more fancy [startindex:endindex:stepsize]\n", "print(a[0, 1:6:2])\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3  4  5  6  7]\n", " [ 8  9 10 11 12 20 14]]\n"]}], "source": ["a[1, 5] = 20\n", "print(a)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  5  4  5  6  7]\n", " [ 8  9  5 11 12 20 14]]\n"]}], "source": ["a[:,2] = 5\n", "print(a)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  1  4  5  6  7]\n", " [ 8  9  3 11 12 20 14]]\n"]}], "source": ["a[:,2] = [1,3]\n", "print(a)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 3d example"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1 2]\n", "  [3 4]]\n", "\n", " [[5 6]\n", "  [7 8]]]\n"]}], "source": ["b = np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])\n", "print(b)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n"]}], "source": ["# Get specific element (work outside in)\n", "print(b[0, 1, 1])\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[3 4]\n", " [7 8]]\n"]}], "source": ["print(b[:,1,:])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# replace\n", "b[:,1,:] = [[9,9,],[8,8]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["INITIALIZE DIFFERENT TYPES OF ARRAYS"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0., 0., 0.],\n", "       [0., 0., 0.]])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# all 0s matrix\n", "np.zeros((2, 3))"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[[1, 1],\n", "        [1, 1]],\n", "\n", "       [[1, 1],\n", "        [1, 1]],\n", "\n", "       [[1, 1],\n", "        [1, 1]],\n", "\n", "       [[1, 1],\n", "        [1, 1]]], dtype=int32)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# all 1s matrix\n", "np.ones((4, 2, 2), dtype='int32')\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[[1., 1., 1.],\n", "        [1., 1., 1.],\n", "        [1., 1., 1.]],\n", "\n", "       [[1., 1., 1.],\n", "        [1., 1., 1.],\n", "        [1., 1., 1.]]])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones((2, 3, 3))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[99, 99],\n", "       [99, 99]])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Any other number\n", "np.full((2, 2), 99)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[4, 4, 4, 4, 4, 4, 4],\n", "       [4, 4, 4, 4, 4, 4, 4]])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Any other number (full_like)\n", "np.full_like(a, 4)\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.87444292, 0.7569381 ],\n", "       [0.25784798, 0.99966577],\n", "       [0.48242505, 0.97329536],\n", "       [0.86098473, 0.41041522]])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Random decimal numbers\n", "np.random.rand(4, 2)\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[2, 0, 1],\n", "       [5, 2, 6],\n", "       [3, 0, 6]], dtype=int32)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Random Integer values\n", "np.random.randint(7, size=(3, 3))\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 0, 0, 0, 0],\n", "       [0, 1, 0, 0, 0],\n", "       [0, 0, 1, 0, 0],\n", "       [0, 0, 0, 1, 0],\n", "       [0, 0, 0, 0, 1]], dtype=int32)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["np.identity(5, dtype='int32')\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 1 1 2 2 2 3 3 3]\n", "[[1 2 3]\n", " [1 2 3]\n", " [1 2 3]]\n"]}], "source": ["# Repeat an array\n", "arr1 = np.array([1, 2, 3])\n", "r1 = np.repeat(arr1, 3, axis=0)\n", "arr2 = np.array([[1, 2, 3]])\n", "r2 = np.repeat(arr2, 3, axis=0)\n", "print(r1)\n", "print(r2)\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1. 1. 1. 1. 1.]\n", " [1. 1. 1. 1. 1.]\n", " [1. 1. 1. 1. 1.]\n", " [1. 1. 1. 1. 1.]\n", " [1. 1. 1. 1. 1.]]\n", "[[0. 0. 0.]\n", " [0. 9. 0.]\n", " [0. 0. 0.]]\n", "[[1. 1. 1. 1. 1.]\n", " [1. 0. 0. 0. 1.]\n", " [1. 0. 9. 0. 1.]\n", " [1. 0. 0. 0. 1.]\n", " [1. 1. 1. 1. 1.]]\n"]}], "source": ["output = np.ones((5, 5))\n", "print(output)\n", "\n", "z = np.zeros((3, 3))\n", "z[1,1] = 9\n", "print(z)\n", "\n", "output[1:4, 1:4] = z\n", "print(output)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Be careful when copying arrays"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3]\n", "[100   2   3]\n"]}], "source": ["a = np.array([1, 2, 3])\n", "b = a.copy()\n", "b[0] = 100\n", "print(a)\n", "print(b)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4]\n"]}], "source": ["a = np.array([1, 2, 3, 4])\n", "print(a)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([3, 4, 5, 6])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["a + 2"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1,  0,  1,  2])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["a - 2"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 4, 6, 8])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["a * 2"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.5, 1. , 1.5, 2. ])"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["a / 2"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2, 2, 4, 4])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["b = np.array([1, 0, 1, 0])\n", "a + b"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1,  4,  9, 16])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["a ** 2"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.84147098,  0.90929743,  0.14112001, -0.7568025 ])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# Take the sin\n", "np.sin(a)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Linear Algebra with NumPy"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1. 1. 1.]\n", " [1. 1. 1.]]\n", "[[2 2]\n", " [2 2]\n", " [2 2]]\n"]}, {"data": {"text/plain": ["array([[6., 6.],\n", "       [6., 6.]])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["a = np.ones((2, 3))\n", "print(a)\n", "\n", "b = np.full((3, 2), 2)\n", "print(b)\n", "\n", "np.mat<PERSON>l(a, b)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.0)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find the determinant\n", "c = np.identity(3)\n", "np.linalg.det(c)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Statistics"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3],\n", "       [4, 5, 6]])"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["stats = np.array([[1, 2, 3], [4, 5, 6]])\n", "stats"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(1)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["np.min(stats)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([3, 6])"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["np.max(stats, axis=1)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 7, 9])"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(stats, axis=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reorganizing Arrays"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 3 4]\n", " [5 6 7 8]]\n", "[[1]\n", " [2]\n", " [3]\n", " [4]\n", " [5]\n", " [6]\n", " [7]\n", " [8]]\n"]}], "source": ["before = np.array([[1, 2, 3, 4], [5, 6, 7, 8]])\n", "print(before)\n", "\n", "after = before.reshape((8, 1))\n", "print(after)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3, 4],\n", "       [5, 6, 7, 8],\n", "       [1, 2, 3, 4],\n", "       [5, 6, 7, 8]])"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["# Vertically stacking vectors\n", "v1 = np.array([1, 2, 3, 4])\n", "v2 = np.array([5, 6, 7, 8])\n", "np.vstack([v1, v2, v1, v2])"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1., 1., 1., 0., 0.],\n", "       [1., 1., 1., 1., 0., 0.]])"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["# Horizontal  stack\n", "h1 = np.ones((2, 4))\n", "h2 = np.zeros((2, 2))\n", "np.hstack((h1, h2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Miscellaneous\n", "  \n", "  Load data from file"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[nan nan]\n"]}], "source": ["filedata = np.genfromtxt('data.txt', delimiter=',')\n", "print(filedata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Boolean masking and advanced indexing"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([False, False])"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["filedata > 50"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([], dtype=float64)"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["filedata[filedata > 50]"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.False_"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["np.any(filedata > 50, axis=0)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.False_"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["np.all(filedata > 50, axis=0)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([False, False])"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["(filedata > 50) & (filedata < 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" "]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}