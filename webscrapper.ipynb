import pandas as pd

# reading 1 csv file form the website
df_premier2425 = pd.read_csv("https://www.football-data.co.uk/mmz4281/2425/E0.csv").head()

df_premier2425.head()

df_premier2425.rename(columns={'FTHG': 'home_goals',
                              'FTAG': 'away_goals'}, inplace=True)

df_premier2425.head()

# create a root variable 
root = "https://www.football-data.co.uk/mmz4281/"

# creating list of leagues
leagues = ["E0", "E1", "E2", "E3", "EC"]
frames = []

# looping through leagues, read mulitple csv and append it into a list 
for league in leagues:
  df = pd.read_csv(root + "2425/" + league + ".csv")
  frames.append(df)

# length of frames
len(frames)

# show 1st, 2nd and 3rd element
frames[0].head(), frames[1].head(), frames[2].head()

for season in range(15, 25):
  print(str(season) + str(season+1))

# creating list of leagues
leagues = ["E0", "E1", "E2", "E3", "EC"]
frames = []

# looping through leagues, read mulitple csv and append it into a list 
for league in leagues:
  for season in range(15, 25):
    df = pd.read_csv(root + str(season) + str(season+1) + "/" + league + ".csv", encoding='latin1')    
    df.insert(1, 'season', season)
    frames.append(df)


# total frames (6 seasons x 3 leagues)