{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a3ab408d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "id": "7c117704", "metadata": {}, "outputs": [], "source": ["data = np.array([[1, 4], [2, 5], [3, 6]])"]}, {"cell_type": "code", "execution_count": null, "id": "31b1f5b3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}